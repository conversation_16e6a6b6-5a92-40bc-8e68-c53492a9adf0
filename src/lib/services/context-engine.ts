import {FileItem} from "@/types/file";
import * as path from 'path';
import {customModel} from "@/lib/ai";
import {generateObject, generateText} from "ai";
import {z} from "zod";
import {SupabaseIntegrationProvider} from "@/lib/integrations/supabase/SupabaseIntegrationProvider";
import {Project} from "@/lib/db/schema";
import {TwoStageLLMContextEngine} from "./two-stage-context-engine";


export type FileType = 'component' | 'screen' | 'context' | 'hook' | 'util' | 'type' | 'config' | 'unknown';

export interface FileMetadata {
    type: FileType;
    exports?: string[];
    imports?: string[];
    lineCount?: number;
    hasJSX?: boolean;
    hasHooks?: boolean;
    hasContext?: boolean;
    hasStyles?: boolean;
}

export interface CodebaseStructure {
    components: string[];
    screens: string[];
    contexts: string[];
    hooks: string[];
    utils: string[];
    types: string[];
    configs: string[];
}

export interface FileRelationships {
    imports: Record<string, string[]>;
    exports: Record<string, string[]>;
    dependencies: Record<string, string[]>;
    dependents: Record<string, string[]>;
}

const ActionPlanSchema = z.object({
    summary: z.string().describe("A brief summary of what needs to be done"),
    complexity: z.enum(["simple", "moderate", "complex"]).describe("The complexity of the task"),
    steps: z.array(z.object({
        description: z.string().describe("Description of the step"),
        fileChanges: z.array(z.object({
            path: z.string().describe("Path to the file"),
            action: z.enum(["create", "modify", "delete"]).describe("Action to take on the file"),
            purpose: z.string().describe("Purpose of the change"),
            priority: z.enum(["high", "medium", "low"]).describe("Priority of the change")
        })).describe("Files that need to be changed for this step")
    })).describe("Steps to implement the requested changes"),
    considerations: z.array(z.string()).describe("Important considerations or potential issues"),
    supabaseChanges: z.array(z.object({
        type: z.enum(["table", "function", "edge_function", "policy", "secret", "trigger", "storage_bucket"]).describe("Type of Supabase change"),
        description: z.string().describe("Description of the change"),
        priority: z.enum(["high", "medium", "low"]).describe("Priority of the change")
    })).optional().describe("Changes needed in Supabase, if applicable")
});


export interface CodeSnippet {
    filePath: string;
    content: string;
    startLine: number;
    endLine: number;
    context?: string; // Additional context like imports or type definitions
    type: 'component' | 'hook' | 'screen' | 'util' | 'type' | 'context' | 'config' | 'unknown';
    score?: number; // Relevance score
    symbols: string[]; // Key symbols defined or used in this snippet
    includesImports?: boolean; // Whether this snippet includes import statements from the top of the file
}

export interface ContextResult {
    query: string;
    relevantFiles: Array<{
        name: string;
        content: string;
        metadata: FileMetadata;
        dependencies: string[];
        dependents: string[];
    }>;
    // New field for code snippets
    snippets?: CodeSnippet[];
    additionalFiles?: string[]
    codebaseStructure: CodebaseStructure;
    relationships: FileRelationships;
    supabaseSchema?: SupabaseSchema | null;
    actionPlan?: z.infer<typeof ActionPlanSchema> | null;
}

export interface SupabaseSchema {
    tables: any[];
    secrets: any[];
    functions: [],
    dbFunctions: [],
    triggers: [],
    rlsPolicies: [],
    storageBuckets: [],
}


/**
 * Context Engine for providing comprehensive codebase understanding in a single query
 * Allows the AI to understand the codebase structure and relationships without iterative file requests
 */
export class ContextEngine {
    private files: FileItem[] = [];
    private fileIndex: Map<string, FileItem> = new Map();
    private dependencyGraph: Map<string, Set<string>> = new Map();
    private fileMetadata: Map<string, FileMetadata> = new Map();
    private codebaseEmbedding: any = null;
    private supabaseSchema: SupabaseSchema | null = null;
    private project: Project | null = null;
    private actionPlan: z.infer<typeof ActionPlanSchema> | null = null;
    private snippetCache: Map<string, CodeSnippet[]> = new Map(); // Cache for extracted snippets
    private astCache: Map<string, any> = new Map(); // Cache for AST data

    /**
     * Initialize the context engine with the project files
     * @param files Array of file items from the project
     * @param project Optional project information for Supabase integration
     */
    constructor(files: FileItem[], project?: Project) {
        this.files = files;
        this.project = project || null;
        this.buildIndex();
        this.analyzeDependencies();
        this.extractMetadata();
    }

    /**
     * Build an index of files for quick lookup
     */
    private buildIndex(): void {
        for (const file of this.files) {
            this.fileIndex.set(file.name, file);
        }
    }

    /**
     * Check if a file should be excluded based on the excludedFiles patterns
     * @param filePath Path of the file to check
     * @param excludedFiles Array of file paths to exclude
     * @returns True if the file should be excluded
     */
    private shouldExcludeFile(filePath: string, excludedFiles: string[]): boolean {
        if (!excludedFiles || excludedFiles.length === 0) {
            return false;
        }

        // Normalize the file path (remove leading slashes, etc.)
        const normalizedPath = filePath.replace(/^\/+/, '');
        const fileName = path.basename(normalizedPath);

        for (const pattern of excludedFiles) {
            // Normalize the pattern
            const normalizedPattern = pattern.replace(/^\/+/, '');

            // Case 1: Exact match (absolute path)
            if (normalizedPath === normalizedPattern) {
                return true;
            }

            // Case 2: Filename match (for unique filenames)
            if (fileName === path.basename(normalizedPattern)) {
                return true;
            }

            // Case 3: Pattern is contained in the path
            if (normalizedPath.includes(normalizedPattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Analyze dependencies between files
     * Builds a graph of which files import/depend on other files
     */
    private analyzeDependencies(): void {
        const importRegex = /import\s+(?:{[^}]*}|\*\s+as\s+\w+|\w+)\s+from\s+['"]([^'"]+)['"]/g;

        for (const file of this.files) {
            if (!file.content || !isCodeFile(file.name)) continue;

            const dependencies = new Set<string>();
            this.dependencyGraph.set(file.name, dependencies);

            let match;
            while ((match = importRegex.exec(file.content)) !== null) {
                const importPath = match[1];

                // Handle relative imports
                if (importPath.startsWith('.')) {
                    const resolvedPath = resolveRelativePath(file.name, importPath);
                    if (this.fileIndex.has(resolvedPath)) {
                        dependencies.add(resolvedPath);
                    }
                }

                // Handle absolute imports (e.g., @/components/...)
                if (importPath.startsWith('@/')) {
                    const absolutePath = importPath.replace('@/', '');
                    const possibleFiles = this.files
                        .map(f => f.name)
                        .filter(name => name.includes(absolutePath));

                    if (possibleFiles.length > 0) {
                        dependencies.add(possibleFiles[0]);
                    }
                }
            }
        }
    }

    /**
     * Extract metadata from files (components, hooks, contexts, etc.)
     */
    private extractMetadata(): void {
        for (const file of this.files) {
            if (!file.content || !isCodeFile(file.name)) continue;

            const metadata: FileMetadata = {
                type: determineFileType(file.name, file.content),
                exports: extractExports(file.content),
                imports: extractImports(file.content),
                lineCount: file.content.split('\n').length,
                hasJSX: file.content.includes('return (') || file.content.includes('return <'),
                hasHooks: file.content.includes('useState') || file.content.includes('useEffect'),
                hasContext: file.content.includes('createContext'),
                hasStyles: file.content.includes('StyleSheet.create'),
            };

            this.fileMetadata.set(file.name, metadata);
        }
    }

    /**
     * Query the context engine for information about the codebase
     * @param query Natural language query about the codebase
     * @param reason
     * @param excludedFiles Optional array of file paths to exclude from analysis
     * @returns Comprehensive information about the relevant parts of the codebase
     */
    async query(query: string, reason: string, excludedFiles?: string[]): Promise<ContextResult> {
        console.time('context-engine-query');
        console.log(`Context engine query started for: "${query}"`, `Excluded file: ${(excludedFiles || []).join(',')}`);

        // First, determine which files are most relevant to the query
        // console.time('find-relevant-files');
        // const relevantFiles = await this.findRelevantFiles(query, excludedFiles || []);
        // console.timeEnd('find-relevant-files');

        // Start parallel operations
        const parallelOperations: any[] = [];

        // 1. Process file contents
        // const fileContentsPromise = Promise.resolve().then(() => {
        //     console.time('process-file-contents');
        //     // Get the content and metadata for these files
        //     const result = relevantFiles.map(fileName => {
        //         const file = this.fileIndex.get(fileName);
        //         const metadata = this.fileMetadata.get(fileName);
        //
        //         return {
        //             name: fileName,
        //             content: file?.content || '',
        //             // metadata: metadata || {type: 'unknown'},
        //             // dependencies: Array.from(this.dependencyGraph.get(fileName) || []),
        //             // dependents: this.findDependents(fileName),
        //         };
        //     });
        //     console.timeEnd('process-file-contents');
        //     return result;
        // });
        // parallelOperations.push(fileContentsPromise);

        // 2. Get the overall codebase structure
        // const codebaseStructurePromise = Promise.resolve().then(() => {
        //     console.time('get-codebase-structure');
        //     const structure = this.getCodebaseStructure();
        //     console.timeEnd('get-codebase-structure');
        //     return structure;
        // });
        // parallelOperations.push(codebaseStructurePromise);

        // 3. Get file relationships (prepare the promise but don't execute yet)
        // const relationshipsPromise = Promise.resolve().then(() => {
        //     console.time('get-file-relationships');
        //     const relationships = this.getFileRelationships(relevantFiles);
        //     console.timeEnd('get-file-relationships');
        //     return relationships;
        // });
        // parallelOperations.push(relationshipsPromise);

        // 4. Extract code snippets using the two-stage approach
        const snippetsPromise = Promise.resolve().then(async () => {
            console.time('extract-code-snippets');

            // Check if we have cached snippets for this query
            const cachedSnippets = this.snippetCache.get(query);
            if (cachedSnippets) {
                console.log('Using cached snippets for query');
                console.timeEnd('extract-code-snippets');
                return cachedSnippets;
            }

            try {
                // Use the TwoStageLLMContextEngine for better snippet extraction
                const twoStageEngine = new TwoStageLLMContextEngine(this.files);
                const result = await twoStageEngine.getRelevantSnippets(query, reason, excludedFiles || []);

                if (result.snippets && result.snippets.length > 0) {
                    // Cache the snippets for future use
                    this.snippetCache.set(query, result.snippets);
                    console.log(`Found ${result.snippets.length} snippets using two-stage approach`);

                    // Log additional files if any
                    // if (result.additionalFiles.length > 0) {
                    //     console.log(`Additional files suggested: ${result.additionalFiles.map(f => f.fileName).join(', ')}`);
                    // }

                    console.timeEnd('extract-code-snippets');
                    return {snippets: result.snippets || [], additionalFiles: result.additionalFiles || []};
                }
                return {snippets: [], additionalFiles: []}
            } catch (error) {
                console.error('Error using TwoStageLLMContextEngine for snippets:', error);
                // Fall through to the original implementation if the new approach fails
            }

            // Fallback to original regex-based extraction
            // console.log('Falling back to regex-based snippet extraction');
            // const allSnippets: CodeSnippet[] = [];
            // for (const fileName of relevantFiles) {
            //     const fileSnippets = this.extractCodeSnippets(fileName, query);
            //     allSnippets.push(...fileSnippets);
            // }

            // Sort snippets by score and take top 10
            // const topSnippets = allSnippets
            //     .sort((a, b) => (b.score || 0) - (a.score || 0))
            //     .slice(0, 10);

            // Cache the snippets for future use
            // this.snippetCache.set(query, topSnippets);

            // console.timeEnd('extract-code-snippets');
            // return topSnippets;
        });
        parallelOperations.push(snippetsPromise);

        // Wait for all parallel operations to complete
        console.time('parallel-operations');
        const [result] = await Promise.all(parallelOperations);
        console.timeEnd('parallel-operations');

        // Performance metrics for debugging
        console.timeEnd('context-engine-query');
        console.log(`Context engine query completed for: "${query}"`);
        // console.log(`- Found ${fileContents?.length} relevant files`);
        console.log(`- Found ${result.snippets?.length} relevant code snippets`);

        return {
            query,
            // relevantFiles: fileContents,
            snippets: result.snippets,
            additionalFiles: result.additionalFiles,
            // codebaseStructure,
            // relationships,
            // supabaseSchema,
            // actionPlan,
        } as ContextResult;
    }

    /**
     * Get Supabase schema information if available
     * @returns Supabase schema information
     */
    async getSupabaseSchema(): Promise<SupabaseSchema | null> {
        if (!this.project || !this.project.supabaseProjectId) {
            return null;
        }

        // If we already have the schema cached, return it
        if (this.supabaseSchema) {
            return this.supabaseSchema;
        }

        try {
            const supabaseProvider = new SupabaseIntegrationProvider();
            const result = await supabaseProvider.getLatestInstructionsForChat({project: this.project});

            const {schema, functions, secrets, dbFunctions, triggers, rlsPolicies, storageBuckets} = result as any;
            // console.log('result', result)
            // Parse the instructions to extract schema information


            // Create a structured representation of the schema
            const parsedSchema: SupabaseSchema = {
                tables: schema,
                functions,
                secrets,
                dbFunctions,
                triggers,
                rlsPolicies,
                storageBuckets
            };

            console.log('parsedSchema', parsedSchema)

            this.supabaseSchema = parsedSchema;
            return parsedSchema;
        } catch (error) {
            console.error('Error fetching Supabase schema:', error);
            return null;
        }
    }

    /**
     * Generate an action plan based on the query and context
     * @param query The user's query
     * @param relevantFiles The relevant files for the query
     * @param supabaseSchema The Supabase schema if available
     * @returns An action plan with steps to implement the requested changes
     */
    async generateActionPlan(query: string, relevantFiles: any[], supabaseSchema: SupabaseSchema | null): Promise<z.infer<typeof ActionPlanSchema>> {
        try {
            // Define the schema for the action plan

            // Prepare context for the AI
            const fileContext = relevantFiles.map(file => {
                return `File: ${file.name}\nType: ${file.metadata.type}\nExports: ${file.metadata.exports?.join(', ') || 'None'}\nImports: ${file.metadata.imports?.slice(0, 5)?.join(', ') || 'None'}${file.metadata.imports?.length > 5 ? '...' : ''}\n`;
            }).join('\n');

            // Prepare Supabase context if available
            let supabaseContext = '';
            if (supabaseSchema) {
                // Add tables information
                if (supabaseSchema.tables && supabaseSchema.tables.length > 0) {
                    supabaseContext += 'Supabase Tables:\n' +
                        supabaseSchema.tables.map(table => {
                            return `Table: ${table.table_name}\nColumns: ${table.columns.map((col: any) =>
                                `${col.column_name} (${col.data_type})`).join(', ')}\n`;
                        }).join('\n');
                }

                // Add Edge Functions information
                if (supabaseSchema.functions && supabaseSchema.functions.length > 0) {
                    supabaseContext += '\nSupabase Edge Functions:\n' +
                        supabaseSchema.functions.map((func: any) =>
                            `- ${func.name || func.slug || 'Unknown function'}${func.entrypoint ? ` (Entrypoint: ${func.entrypoint})` : ''}`
                        ).join('\n');
                }

                // Add Database Functions information
                if (supabaseSchema.dbFunctions && supabaseSchema.dbFunctions.length > 0) {
                    supabaseContext += '\nSupabase Database Functions:\n' +
                        supabaseSchema.dbFunctions.map((func: any) =>
                            `- ${func.name || 'Unknown function'}${func.schema ? ` (Schema: ${func.schema})` : ''}`
                        ).join('\n');
                }

                // Add RLS Policies information
                if (supabaseSchema.rlsPolicies && supabaseSchema.rlsPolicies.length > 0) {
                    supabaseContext += '\nSupabase RLS Policies:\n' +
                        supabaseSchema.rlsPolicies.map((policy: any) =>
                            `- ${policy.name || 'Unknown policy'} on ${policy.table || 'Unknown table'} (${policy.action || 'Unknown action'})`
                        ).join('\n');
                }

                // Add Triggers information
                if (supabaseSchema.triggers && supabaseSchema.triggers.length > 0) {
                    supabaseContext += '\nSupabase Triggers:\n' +
                        supabaseSchema.triggers.map((trigger: any) =>
                            `- ${trigger.name || 'Unknown trigger'} on ${trigger.table || 'Unknown table'}`
                        ).join('\n');
                }

                // Add Storage Buckets information
                if (supabaseSchema.storageBuckets && supabaseSchema.storageBuckets.length > 0) {
                    supabaseContext += '\nSupabase Storage Buckets:\n' +
                        supabaseSchema.storageBuckets.map((bucket: any) =>
                            `- ${bucket.name || 'Unknown bucket'} (Public: ${bucket.public ? 'Yes' : 'No'})${bucket.file_size_limit ? ` (Size Limit: ${bucket.file_size_limit} bytes)` : ''}`
                        ).join('\n');
                }
            }

            // Generate the action plan using AI
            const result = await generateObject({
                model: customModel('openai/gpt-4.1'),
                temperature: 0.1,
                schema: ActionPlanSchema,
                system: `You are an expert software architect and developer specializing in React Native Expo applications with Supabase integration.
      Your task is to create a detailed action plan for implementing the requested changes based on the provided codebase context.
      Focus on creating a practical, step-by-step plan that addresses all aspects of the request.
      Be specific about which files need to be changed and why.

      If Supabase integration is involved:
      1. Consider all available Supabase resources (tables, edge functions, database functions, RLS policies, triggers, storage buckets)
      2. Recommend appropriate changes to database schema when needed
      3. Suggest Edge Functions for complex server-side operations
      4. Include necessary RLS policy updates for proper security
      5. Utilize Storage buckets for file uploads when appropriate
      6. Consider database triggers for automated operations

      Provide a comprehensive plan that leverages all available resources efficiently.`,
                prompt: `Based on the following query and codebase context, create a detailed action plan for implementing the requested changes.

Query: ${query}

Codebase Context:
${fileContext}

${supabaseContext ? supabaseContext : 'No Supabase integration detected.'}

Please provide a comprehensive action plan with specific steps, file changes, and considerations.`
            });

            return result.object;
        } catch (error) {
            console.error('Error generating action plan:', error);
            // Return a basic action plan if generation fails
            return {
                summary: `Implement changes for: ${query}`,
                complexity: "moderate",
                steps: [{
                    description: "Analyze and implement the requested changes",
                    fileChanges: relevantFiles.slice(0, 3).map(file => ({
                        path: file.name,
                        action: "modify",
                        purpose: "Implement requested changes",
                        priority: "high"
                    }))
                }],
                considerations: ["Consider the existing codebase structure", "Ensure compatibility with current implementation"],
                supabaseChanges: supabaseSchema ? [
                    {
                        type: "table",
                        description: "May need database schema changes to support the new feature",
                        priority: "medium"
                    },
                    supabaseSchema.functions?.length > 0 ? {
                        type: "edge_function",
                        description: "May need to update Edge Functions to support the new feature",
                        priority: "medium"
                    } : null,
                    supabaseSchema.storageBuckets?.length > 0 ? {
                        type: "storage_bucket",
                        description: "May need to configure Storage for file uploads",
                        priority: "medium"
                    } : null,
                    supabaseSchema.rlsPolicies?.length > 0 ? {
                        type: "policy",
                        description: "May need to update RLS policies for proper access control",
                        priority: "high"
                    } : null
                ].filter(Boolean) as any : []
            };
        }
    }


    /**
     * Find files that are relevant to the given query
     * @param query Natural language query
     * @param excludedFiles Array of file paths to exclude from analysis
     * @returns Array of file names that are relevant to the query
     */
    private async findRelevantFiles(query: string, excludedFiles: string[]): Promise<string[]> {
        // Get the list of files that are not excluded
        const availableFiles = this.files.filter(file => !this.shouldExcludeFile(file.name, excludedFiles));

        // Use AI to determine which files are most relevant to the query
        const result = await generateObject({
            model: customModel('openai/gpt-4.1'),
            temperature: 0.1,
            schema: z.object({files: z.array(z.string())}),
            system: `You are a code analysis expert. Your task is to identify which files in a React Native Expo project are most relevant to a specific query.

You will be given:
1. A query about the codebase
2. A list of files in the project with their types

Return ONLY a JSON array of the most relevant file paths, with a maximum of 5 files. Choose files that would be most helpful for understanding or implementing the query.`,
            prompt: `Query: ${query}

Files in the project:
${availableFiles.map(file => `${file.name} - ${this.fileMetadata.get(file.name)?.type || 'unknown'}`).join('\n')}

${excludedFiles.length > 0 ? `Note: The following files have been excluded from analysis: ${excludedFiles.join(', ')}\n\n` : ''}
Return ONLY a JSON array of the most relevant file paths (maximum 5):`,
        });

        try {
            // console.log('result', result.object)
            // Parse the result as a JSON array
            const fileList = result.object.files;
            if (Array.isArray(fileList) && fileList.length > 0) {
                return fileList.filter(file => this.fileIndex.has(file));
            }
        } catch (error) {
            console.error('Error parsing relevant files:', error);
        }

        // Fallback: return the most common file types if AI parsing fails
        return this.files
            .filter(file => isCodeFile(file.name) && !this.shouldExcludeFile(file.name, excludedFiles))
            .map(file => file.name)
            .slice(0, 5);
    }

    /**
     * Find all files that depend on the given file
     * @param fileName Name of the file
     * @returns Array of file names that depend on the given file
     */
    private findDependents(fileName: string): string[] {
        const dependents: string[] = [];

        for (const [file, dependencies] of this.dependencyGraph.entries()) {
            if (dependencies.has(fileName)) {
                dependents.push(file);
            }
        }

        return dependents;
    }

    /**
     * Get the overall structure of the codebase
     * @returns Object representing the codebase structure
     */
    private getCodebaseStructure(): CodebaseStructure {
        const structure: CodebaseStructure = {
            components: [],
            screens: [],
            contexts: [],
            hooks: [],
            utils: [],
            types: [],
            configs: [],
        };

        for (const [fileName, metadata] of this.fileMetadata.entries()) {
            switch (metadata.type) {
                case 'component':
                    structure.components.push(fileName);
                    break;
                case 'screen':
                    structure.screens.push(fileName);
                    break;
                case 'context':
                    structure.contexts.push(fileName);
                    break;
                case 'hook':
                    structure.hooks.push(fileName);
                    break;
                case 'util':
                    structure.utils.push(fileName);
                    break;
                case 'type':
                    structure.types.push(fileName);
                    break;
                case 'config':
                    structure.configs.push(fileName);
                    break;
            }
        }

        return structure;
    }

    /**
     * Get relationships between files
     * @param fileNames Array of file names
     * @returns Object representing relationships between files
     */
    private getFileRelationships(fileNames: string[]): FileRelationships {
        const relationships: FileRelationships = {
            imports: {},
            exports: {},
            dependencies: {},
            dependents: {},
        };

        for (const fileName of fileNames) {
            relationships.imports[fileName] = this.fileMetadata.get(fileName)?.imports || [];
            relationships.exports[fileName] = this.fileMetadata.get(fileName)?.exports || [];
            relationships.dependencies[fileName] = Array.from(this.dependencyGraph.get(fileName) || []);
            relationships.dependents[fileName] = this.findDependents(fileName);
        }

        return relationships;
    }
}

/**
 * Determine if a file is a code file
 * @param fileName Name of the file
 * @returns True if the file is a code file
 */
function isCodeFile(fileName: string): boolean {
    const ext = path.extname(fileName).toLowerCase();
    return ['.ts', '.tsx', '.js', '.jsx'].includes(ext);
}

/**
 * Determine the type of a file based on its name and content
 * @param fileName Name of the file
 * @param content Content of the file
 * @returns Type of the file
 */
function determineFileType(fileName: string, content: string): FileType {
    const name = fileName.toLowerCase();

    if (name.includes('screen') || name.includes('page')) {
        return 'screen';
    }

    if (name.includes('context')) {
        return 'context';
    }

    if (name.includes('hook') || (content && content.includes('use') && content.includes('return {'))) {
        return 'hook';
    }

    if (name.includes('util') || name.includes('helper')) {
        return 'util';
    }

    if (name.includes('type') || name.includes('interface') || name.includes('.d.ts')) {
        return 'type';
    }

    if (name.includes('config') || name.includes('setup')) {
        return 'config';
    }

    // Default to component for TSX/JSX files
    if (name.endsWith('.tsx') || name.endsWith('.jsx')) {
        return 'component';
    }

    return 'unknown';
}

/**
 * Extract exports from a file
 * @param content Content of the file
 * @returns Array of exports
 */
function extractExports(content: string): string[] {
    const exports: string[] = [];
    const exportRegex = /export\s+(const|function|class|interface|type|default)\s+(\w+)/g;

    let match;
    while ((match = exportRegex.exec(content)) !== null) {
        exports.push(match[2]);
    }

    return exports;
}

/**
 * Extract imports from a file
 * @param content Content of the file
 * @returns Array of imports
 */
function extractImports(content: string): string[] {
    const imports: string[] = [];
    const importRegex = /import\s+(?:{([^}]*)}|\*\s+as\s+(\w+)|(\w+))\s+from/g;

    let match;
    while ((match = importRegex.exec(content)) !== null) {
        const namedImports = match[1];
        const namespaceImport = match[2];
        const defaultImport = match[3];

        if (namedImports) {
            imports.push(...namedImports.split(',').map(s => s.trim()));
        }

        if (namespaceImport) {
            imports.push(namespaceImport);
        }

        if (defaultImport) {
            imports.push(defaultImport);
        }
    }

    return imports;
}

/**
 * Resolve a relative import path
 * @param currentFile Current file path
 * @param importPath Import path
 * @returns Resolved file path
 */
function resolveRelativePath(currentFile: string, importPath: string): string {
    const directory = path.dirname(currentFile);
    let resolvedPath = path.join(directory, importPath);

    // Handle index files
    if (!path.extname(resolvedPath)) {
        resolvedPath += '.tsx';

        // If .tsx doesn't exist, try .ts
        // Note: In a real implementation, you'd check if the file exists
        if (!resolvedPath.endsWith('.tsx')) {
            resolvedPath = resolvedPath.replace(/\.tsx$/, '.ts');
        }
    }

    return resolvedPath;
}

// Types

