import { z } from "zod";
import { generateObject } from "ai";
import { customModel } from "@/lib/ai";
import { FileItem } from "@/types/file";
import { CodeSnippet } from "./context-engine";
import * as path from "path";
import {orderBy} from "lodash";

/**
 * Interface for snippet identification result from the second LLM
 */
interface SnippetIdentification {
  fileName: string;
  startLine: number;
  endLine: number;
  snippetType: string;
  snippetName: string;
  relevanceScore: number;
  reasoning: string;
}

/**
 * Two-stage LLM approach for context retrieval
 * 1. First LLM identifies relevant files
 * 2. Second LLM identifies relevant snippets within those files
 */
export class TwoStageLLMContextEngine {
  private files: FileItem[] = [];
  private fileIndex: Map<string, FileItem> = new Map();

  /**
   * Initialize with project files
   */
  constructor(files: FileItem[]) {
    this.files = files;
    console.log(`🔧 Building index for ${files.length} files`);
    console.log(`📁 Sample files: ${files.slice(0, 5).map(f => f.name).join(', ')}...`);
    this.buildIndex();
    console.log(`📚 Index built with ${this.fileIndex.size} files`);
  }

  /**
   * Build an index of files for quick lookup
   * Normalize paths to handle various path patterns
   */
  private buildIndex(): void {
    for (const file of this.files) {
      // Store with original path
      this.fileIndex.set(file.name, file);

      // Normalize various path patterns
      let normalizedPath = file.name;

      // Remove project directory prefix
      normalizedPath = normalizedPath.replace(/^project-[a-f0-9-]+\//, '');

      // Remove leading slashes and dots
      normalizedPath = normalizedPath.replace(/^[\.\/]+/, '');

      // Handle src/ prefix variations
      if (normalizedPath.startsWith('src/')) {
        this.fileIndex.set(normalizedPath.substring(4), file); // without src/
      }

      // Store normalized path if different
      if (normalizedPath !== file.name) {
        this.fileIndex.set(normalizedPath, file);
      }

      // Also store with lib/ and src/lib/ variations for common patterns
      if (normalizedPath.includes('lib/')) {
        const libVariant = normalizedPath.replace(/^.*lib\//, 'lib/');
        this.fileIndex.set(libVariant, file);
        this.fileIndex.set('src/' + libVariant, file);
      }
    }
  }

  /**
   * Get a minimal structural representation of the codebase
   * This provides enough context for the first LLM while keeping token usage low
   */
  private getCodebaseStructure(): string {
    // Build a map of imports for quick lookup
    const importMap = new Map<string, Set<string>>();

    // First pass: extract imports from all files
    this.files
      .filter(file => this.isCodeFile(file.name))
      .forEach(file => {
        const imports = this.extractImports(file.content || "");
        imports.forEach(importPath => {
          // Normalize import path
          if (importPath.startsWith('@/')) {
            importPath = importPath.substring(2);
          }

          // Add to the import map
          if (!importMap.has(importPath)) {
            importMap.set(importPath, new Set());
          }
          importMap.get(importPath)?.add(file.name);
        });
      });

    // Second pass: create the structure with import relationships
    return this.files
      .filter(file => this.isCodeFile(file.name))
      .map(file => {
        const fileType = this.determineFileType(file.name, file.content || "");
        const exports = this.extractExports(file.content || "");

        // Find files that import this file (limited to 3 for brevity)
        const importedBy = Array.from(importMap.get(file.name) || []).slice(0, 3);

        let result = `${file.name} - ${fileType}`;

        if (exports.length > 0) {
          result += ` - Exports: ${exports.join(", ")}`;
        }

        if (importedBy.length > 0) {
          result += ` - Used by: ${importedBy.join(", ")}`;
        }

        return result;
      })
      .join("\n");
  }

  /**
   * First stage: Find relevant files for a query
   */
  async findRelevantFiles(query: string, reason: string, excludedFiles: string[] = []): Promise<{primaryFiles: string[], secondaryFiles: string[], reasoning: string }> {
    console.time('find-relevant-files');

    // Get a compact representation of the codebase structure
    const codebaseStructure = this.getCodebaseStructure();

    // Use LLM to identify relevant files
    const result = await generateObject({
      model: customModel('openai/gpt-4.1-nano'), // Using a smaller model to reduce costs
      temperature: 0.1,
      schema: z.object({
        files: z.array(z.string()),
        reasoning: z.string().describe("Explanation of why these files were selected")
      }),
      system: `You are a code analysis expert. Your task is to identify which files in a React Native Expo project are most relevant to a specific query.

You will be given:
1. A query about the codebase
2. A structured representation of files in the project with their types and exports
3. Include only files that are present in the codebase and nothing else. DO NOT hallucinate or makeup files.

NOTES:
1. REQUIREMENTS.md is like the README.md for the system, include it in instances even if it is not directly asked for but may contain the relevant details about the query
2. MINI_MISSION.md: A single session mission file similar to a EPIC/GROUP of user stories. Include if relevant and not even asked for.
3. LEARNINGS.md: Direct personalized learnings of the current project, specifically the user's preferences etc.

Return a JSON object with:
1. An array of the most relevant file paths (maximum 8)
2. Your reasoning for selecting these files

CRITICAL: FIND FILES FOR MINIMUM VIABLE CONTEXT, NOT JUST IMPLEMENTATION
- IMPLEMENTATION files: Core components/functions that directly answer the query (1-2 files)
- USAGE files: Files that import/use the implementation - screens, components, examples (2-3 files)
- CONTEXT files: Configuration, types, styling that affects behavior (1-2 files)
- RELATED files: Similar components that might conflict or provide patterns (1 file)

For "find bug" queries: MUST include usage examples to see how the component is actually used
For "how X works" queries: MUST include both implementation and real usage patterns
For "architecture" queries: MUST include multiple related files showing patterns

ALWAYS prioritize showing the PRIMARY LLM how things are used in practice, not just how they're implemented.`,
      prompt: `Query: ${query}
      Reason: ${reason}

Files in the project:
${codebaseStructure}

${excludedFiles.length > 0 ? `Note: The following files have been excluded from analysis: ${excludedFiles.join(', ')}\n\n` : ''}

IMPORTANT: For debugging queries like "find bug" or "rendering issues", you MUST include:
1. The main implementation file
2. Files that import/use the component (look for import statements)
3. Screen/component files that might use the component
4. Related styling or configuration files

Do not just return the implementation file - include usage examples to help debug the issue.

Return the most relevant file paths (maximum 8) and your reasoning:`,
    });

    console.timeEnd('find-relevant-files');
    console.log(`Selected files reasoning: ${result.object.reasoning}`);

    // Debug: Log what files were selected and what's in the index
    console.log(`Files selected by LLM: ${result.object.files.join(', ')}`);
    console.log(`Files in index: ${Array.from(this.fileIndex.keys()).slice(0, 10).join(', ')}... (showing first 10)`);

    // Filter out any files that don't exist in our index
    const validFiles = result.object.files.filter(file => {
      const exists = this.fileIndex.has(file);
      if (!exists) {
        console.log(`❌ File not found in index: ${file}`);
      }
      return exists;
    });

    console.log(`Valid files after filtering: ${validFiles.join(', ')}`);

    // Split into primary (first 6) and secondary (remaining) files for better coverage
    const primaryFiles = validFiles.slice(0, 6);
    const secondaryFiles = validFiles.slice(6);

    return {
      primaryFiles,
      secondaryFiles,
      reasoning: result.object.reasoning
    };
  }

  /**
   * Second stage: Identify relevant snippets within files
   */
  async identifyRelevantSnippets(query: string, reason: string, relevantFiles: string[], reasoning: string): Promise<SnippetIdentification[]> {
    console.time('identify-snippets');


    // Prepare file contents with line numbers and token limits
    const fileContents = relevantFiles.map(fileName => {
      const file = this.fileIndex.get(fileName);
      const content = file?.content || "";

      // Add line numbers to help the LLM identify specific ranges
      // Format with consistent padding to make line numbers stand out
      const lines = content.split("\n");

      // Limit file size to prevent token explosion (max 300 lines per file)
      const maxLinesPerFile = 300;
      const truncatedLines = lines.length > maxLinesPerFile
        ? [...lines.slice(0, maxLinesPerFile - 10), '// ... truncated for brevity ...', ...lines.slice(-10)]
        : lines;

      const maxLineNumberWidth = String(truncatedLines.length).length;
      const numberedContent = truncatedLines.map((line, i) => {
        const lineNumber = lines.length > maxLinesPerFile && i >= maxLinesPerFile - 10
          ? lines.length - (truncatedLines.length - i - 1) // Adjust line numbers for truncated content
          : i + 1;
        const paddedLineNumber = String(lineNumber).padStart(maxLineNumberWidth, ' ');
        return `${paddedLineNumber}: ${line}`;
      }).join("\n");

      return {
        name: fileName,
        content: numberedContent,
        lineCount: lines.length,
        wasTruncated: lines.length > maxLinesPerFile
      };
    });

    // Use LLM to identify relevant snippets within these files
    const result = await generateObject({
      model: customModel('openai/gpt-4.1-mini'), // Using a more capable model for this task
      temperature: 0.1,
      schema: z.object({
        snippets: z.array(z.object({
          fileName: z.string(),
          startLine: z.number(),
          endLine: z.number(),
          snippetType: z.string(),
          snippetName: z.string(),
          relevanceScore: z.number().min(0).max(1),
          reasoning: z.string()
        }))
      }),
      system: `You are a code analysis expert. Your task is to identify the most relevant code snippets within files for a specific query.

For each file, identify specific code blocks (functions, components, types, etc.) that are most relevant to the query.
Return the exact line numbers for each snippet, along with metadata about the snippet.

CRITICAL: PROVIDE MINIMUM VIABLE CONTEXT FOR UNDERSTANDING
1. Focus on giving the PRIMARY LLM enough context to understand and make decisions
2. Include both IMPLEMENTATION and USAGE patterns when relevant
3. For "how X works" queries: Show implementation + key usage examples
4. For "find bug" queries: Show implementation + usage patterns that could reveal issues
5. For "architecture" queries: Show multiple patterns and their relationships

SNIPPET SELECTION STRATEGY:
6. IMPLEMENTATION: Core functions/components (score 1.0, max 100 lines per snippet)
7. USAGE: Call sites and examples (score 0.8, max 40 lines per snippet)
8. CONTEXT: Related configs/types (score 0.6, max 25 lines per snippet)
9. Maximum 4 snippets total per query - prefer multiple focused snippets over few large ones
10. Each snippet should serve a specific purpose in helping the PRIMARY LLM understand
11. When showing multiple usage patterns, include 1-2 representative examples, not all
12. For large functions/components, extract the most relevant sections rather than entire implementations
13. Prioritize snippets that help the PRIMARY LLM understand relationships and data flow

1. REQUIREMENTS.md is like the README.md for the system, include it in instances even if it is not directly asked for but may contain the relevant details about the query
2. MINI_MISSION.md: A single session mission file similar to a EPIC/GROUP of user stories. Include if relevant and not even asked for
3. LEARNINGS.md: Direct personalized learnings of the current project, specifically the user's preferences etc

These markdown files are large and hence should be filtered to return what is relevant even if not explicitly asked for. Its a judgement call. Balance between context stuffing and focussing on the right areas/code/feature.
`,
      prompt: `Query: ${query}

I need to identify code snippets that give the PRIMARY LLM minimum viable context to understand and make decisions. For each relevant code block, provide:
1. The file name
2. Start and end line numbers (inclusive) - use the exact line numbers shown at the beginning of each line
3. Type of snippet (implementation, usage, context, config)
4. Name of the snippet (function name, component name, etc.)
5. Relevance score (0.0 to 1.0)
6. Brief reasoning for why this snippet helps answer the query

SNIPPET STRATEGY FOR UNDERSTANDING:
- IMPLEMENTATION (score 1.0): Core functions/components that directly implement the requested functionality
- USAGE (score 0.8): Call sites, examples, integration patterns that show how things are used (15-30 lines)
- CONTEXT (score 0.6): Related configs, types, setup that provide necessary background (10-20 lines)
- Include 2-3 usage examples rather than all usage patterns
- Show relationships and data flow, not just isolated implementations

GUIDELINES:
- Each line in the files below is prefixed with its line number (e.g., "42: const foo = bar;"). Use these exact line numbers in your response.
- For implementation snippets: Include complete functions with necessary imports
- For usage snippets: Include just the relevant call sites and immediate context
- For context snippets: Include key configurations, types, or setup information
- Prioritize snippets that help understand "how things connect" over "complete implementations"

Reasoning from the user/llm issuing the original query on why they want the result. This gets fed to the first engine:
${reason}

Reasoning from the first engine on why each file was selected. Use the reasoning to understand the whole flow and to select snippets accordingly:
${reasoning}

Files to analyze:
${fileContents.map(file => `
=== ${file.name} (${file.lineCount} lines) ===
${file.content}
`).join("\n\n")}

Return an array of the most relevant code snippets with their exact line numbers and metadata:`,
    });

    console.timeEnd('identify-snippets');
    return result.object.snippets;
  }

  /**
   * Smart truncation to fit within line budget while preserving understanding context
   */
  smartTruncateSnippets(snippets: CodeSnippet[], maxLines: number = 600): {truncatedSnippets: CodeSnippet[], additionalFromTruncation: {fileName: string, reason: string, suggestedQuery: string}[]} {
    // Categorize snippets by type for strategic selection
    const implementation = snippets.filter(s => (s.score || 0) >= 0.9);
    const usage = snippets.filter(s => (s.score || 0) >= 0.7 && (s.score || 0) < 0.9);
    const context = snippets.filter(s => (s.score || 0) < 0.7);

    let currentLines = 0;
    const truncatedSnippets: CodeSnippet[] = [];
    const additionalFromTruncation: {fileName: string, reason: string, suggestedQuery: string}[] = [];

    // Strategy: Always include implementation, then usage, then context within budget
    const prioritizedSnippets = [
      ...orderBy(implementation, ['score'], ['desc']).slice(0, 2), // Max 2 implementation snippets
      ...orderBy(usage, ['score'], ['desc']).slice(0, 2), // Max 2 usage examples
      ...orderBy(context, ['score'], ['desc']).slice(0, 1)  // Max 1 context snippet
    ];

    for (const snippet of prioritizedSnippets) {
      const snippetLines = snippet.content.split('\n').length;

      if (currentLines + snippetLines <= maxLines) {
        truncatedSnippets.push(snippet);
        currentLines += snippetLines;
      } else {
        // Add to additional files instead of truncating content
        additionalFromTruncation.push({
          fileName: snippet.filePath,
          reason: `Relevant but truncated due to 1000-line budget (relevance: ${(snippet.score || 0).toFixed(2)}, ${snippetLines} lines)`,
          suggestedQuery: `Show ${snippet.symbols.join(', ')} in ${snippet.filePath}`
        });
      }
    }

    // Add any remaining snippets to additional files
    const remainingSnippets = snippets.filter(s => !truncatedSnippets.includes(s) && !additionalFromTruncation.some(a => a.fileName === s.filePath));
    for (const snippet of remainingSnippets) {
      additionalFromTruncation.push({
        fileName: snippet.filePath,
        reason: `Additional context available (relevance: ${(snippet.score || 0).toFixed(2)})`,
        suggestedQuery: `Show ${snippet.symbols.join(', ')} in ${snippet.filePath}`
      });
    }

    console.log(`Smart truncation: kept ${truncatedSnippets.length}/${snippets.length} snippets within ${maxLines} line budget (${currentLines} lines total)`);
    console.log(`  - Implementation: ${implementation.length} found, ${truncatedSnippets.filter(s => (s.score || 0) >= 0.9).length} kept`);
    console.log(`  - Usage: ${usage.length} found, ${truncatedSnippets.filter(s => (s.score || 0) >= 0.7 && (s.score || 0) < 0.9).length} kept`);
    console.log(`  - Context: ${context.length} found, ${truncatedSnippets.filter(s => (s.score || 0) < 0.7).length} kept`);

    return {truncatedSnippets, additionalFromTruncation};
  }

  /**
   * Extract actual code snippets based on line numbers
   */
  extractSnippets(snippetIdentifications: SnippetIdentification[]): CodeSnippet[] {
    // Group snippets by file to avoid duplicate processing
    const snippetsByFile = new Map<string, SnippetIdentification[]>();

    for (const snippet of snippetIdentifications) {
      if (!snippetsByFile.has(snippet.fileName)) {
        snippetsByFile.set(snippet.fileName, []);
      }
      snippetsByFile.get(snippet.fileName)?.push(snippet);
    }

    const results: CodeSnippet[] = [];

    // Process each file's snippets
    for (const [fileName, fileSnippets] of snippetsByFile.entries()) {
      const file = this.fileIndex.get(fileName);
      if (!file || !file.content) continue;

      const lines = file.content.split("\n");

      // Find import statements (usually at the top of the file)
      const importEndLine = this.findImportEndLine(lines);

      // Process each snippet in the file
      for (const identification of fileSnippets) {
        // Ensure line numbers are within bounds
        const startLine = Math.max(1, Math.min(identification.startLine, lines.length));
        const endLine = Math.max(startLine, Math.min(identification.endLine, lines.length));

        // Always include imports to prevent import errors - small cost, big benefit
        const shouldIncludeImports = importEndLine > 0;

        // Determine if we should include imports
        // const shouldIncludeImports = hasImports &&
        //     identification.snippetType.toLowerCase() !== 'import' &&
        //     startLine > importEndLine;

        // Extract the snippet content with imports if needed
        let snippetLines: string[];
        let actualStartLine: number;

        if (shouldIncludeImports) {
          // Include imports and the actual snippet
          const importLines = lines.slice(0, importEndLine);
          const codeLines = lines.slice(startLine - 1, endLine);

          // Add a separator between imports and code
          snippetLines = [...importLines, '', '// Main snippet', ...codeLines];
          actualStartLine = 1; // Starting from the beginning of the file
        } else {
          // Just include the snippet itself
          snippetLines = lines.slice(startLine - 1, endLine);
          actualStartLine = startLine;
        }

        const content = snippetLines.join("\n");

        // Log the extraction for debugging
        console.log(`Extracting snippet from ${identification.fileName}:`);
        console.log(`  Lines ${startLine}-${endLine} (${snippetLines.length} lines)`);
        console.log(`  Type: ${identification.snippetType}, Name: ${identification.snippetName}`);
        console.log(`  Relevance: ${identification.relevanceScore.toFixed(2)}`);
        if (shouldIncludeImports) {
          console.log(`  Including imports from lines 1-${importEndLine}`);
        }

        results.push({
          filePath: identification.fileName,
          content,
          startLine: actualStartLine,
          endLine: shouldIncludeImports ? endLine + (importEndLine + 2) : endLine, // Adjust for added separator lines
          type: this.mapSnippetType(identification.snippetType),
          symbols: [identification.snippetName],
          score: identification.relevanceScore,
          context: identification.reasoning,
          includesImports: shouldIncludeImports
        } as CodeSnippet);
      }
    }

    return orderBy(results, ['score'], ['desc']);
  }

  /**
   * Find the line where imports end in a file
   */
  private findImportEndLine(lines: string[]): number {
    let lastImportLine = 0;

    for (let i = 0; i < Math.min(lines.length, 50); i++) { // Only check first 50 lines
      const line = lines[i].trim();
      if (line.startsWith('import ')) {
        lastImportLine = i + 1; // +1 because line numbers are 1-based
      }
    }

    return lastImportLine;
  }

  /**
   * Main method to get relevant snippets for a query
   */
  async getRelevantSnippets(query: string, reason: string, excludedFiles: string[] = []): Promise<{snippets: CodeSnippet[], additionalFiles: {fileName: string, reason: string, suggestedQuery: string}[]}> {
    // Stage 1: Find relevant files
    const {primaryFiles, secondaryFiles, reasoning} = await this.findRelevantFiles(query, reason, excludedFiles);

    if (primaryFiles.length === 0) {
      console.log("No relevant files found");
      return {snippets: [], additionalFiles: []};
    }

    // Stage 2: Identify relevant snippets within primary files
    const snippetIdentifications = await this.identifyRelevantSnippets(query, reason, primaryFiles, reasoning);

    // Stage 3: Extract the actual snippets with smart truncation
    const allSnippets = this.extractSnippets(snippetIdentifications);
    const {truncatedSnippets, additionalFromTruncation} = this.smartTruncateSnippets(allSnippets, 600); // 600 line budget for more aggressive cost reduction

    // Stage 4: Prepare additional files suggestions
    const additionalFiles = [
      ...secondaryFiles.map(file => ({
        fileName: file,
        reason: "May contain related functionality",
        suggestedQuery: `Show specific functions in ${file} related to: ${query}`
      })),
      ...additionalFromTruncation
    ];

    return {snippets: truncatedSnippets, additionalFiles};
  }

  /**
   * Get relevant snippets with additional files context for LLM
   * Returns both snippets and a formatted string of additional files for LLM context
   */
  async getRelevantSnippetsWithContext(query: string, reason: string, excludedFiles: string[] = []): Promise<{snippets: CodeSnippet[], additionalFilesContext: string}> {
    const result = await this.getRelevantSnippets(query, reason, excludedFiles);

    let additionalFilesContext = "";
    if (result.additionalFiles.length > 0) {
      additionalFilesContext = "\n\n## Additional Relevant Files (not included due to budget constraints):\n";
      result.additionalFiles.forEach((file, index) => {
        additionalFilesContext += `${index + 1}. **${file.fileName}** - ${file.reason}\n`;
      });
      additionalFilesContext += "\nNote: These files may contain additional relevant context. Consider querying them if the current snippets don't provide sufficient information.\n";
    }

    return {
      snippets: result.snippets,
      additionalFilesContext
    };
  }

  /**
   * Helper methods
   */
  private isCodeFile(fileName: string): boolean {
    const ext = path.extname(fileName).toLowerCase();
    return ['.ts', '.tsx', '.js', '.jsx'].includes(ext);
  }

  private determineFileType(fileName: string, content: string): string {
    const name = fileName.toLowerCase();

    if (name.includes('screen') || name.includes('page')) {
      return 'screen';
    }

    if (name.includes('context')) {
      return 'context';
    }

    if (name.includes('hook') || (content && content.includes('use') && content.includes('return {'))) {
      return 'hook';
    }

    if (name.includes('util') || name.includes('helper')) {
      return 'util';
    }

    if (name.includes('type') || name.includes('interface') || name.includes('.d.ts')) {
      return 'type';
    }

    if (name.includes('config') || name.includes('setup')) {
      return 'config';
    }

    // Default to component for TSX/JSX files
    if (name.endsWith('.tsx') || name.endsWith('.jsx')) {
      return 'component';
    }

    return 'unknown';
  }

  private extractExports(content: string): string[] {
    const exports: string[] = [];
    const exportRegex = /export\s+(const|function|class|interface|type|default)\s+(\w+)/g;

    let match: RegExpExecArray | null;
    while ((match = exportRegex.exec(content)) !== null) {
      exports.push(match[2]);
    }

    return exports;
  }

  private extractImports(content: string): string[] {
    const imports: string[] = [];
    const importRegex = /import\s+(?:{[^}]*}|\*\s+as\s+\w+|\w+)\s+from\s+['"]([@\w\/.\-]+)['"];?/g;

    let match: RegExpExecArray | null;
    while ((match = importRegex.exec(content)) !== null) {
      const importPath = match[1];
      imports.push(importPath);
    }

    return imports;
  }

  private mapSnippetType(type: string): 'component' | 'hook' | 'screen' | 'util' | 'type' | 'context' | 'config' | 'unknown' {
    const normalizedType = type.toLowerCase();

    if (normalizedType.includes('component')) return 'component';
    if (normalizedType.includes('hook')) return 'hook';
    if (normalizedType.includes('screen')) return 'screen';
    if (normalizedType.includes('util') || normalizedType.includes('function')) return 'util';
    if (normalizedType.includes('type') || normalizedType.includes('interface')) return 'type';
    if (normalizedType.includes('context') || normalizedType.includes('provider')) return 'context';
    if (normalizedType.includes('config')) return 'config';

    return 'unknown';
  }
}
