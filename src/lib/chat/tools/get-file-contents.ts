import {DataStreamWriter, tool} from "ai";
import {FileLineManager} from "@/lib/editor/FileLineManager";
import {FileItem} from "@/types/file";
import {z} from "zod";
import {CreditUsageTracker} from "@/lib/credit/CreditUsageTracker";

export const getFileContents = ({
                                    fileManager,
                                    dataStream,
                                    creditUsageTracker
                                }: {
    fileManager: FileLineManager,
    dataStream: DataStreamWriter,
    creditUsageTracker: CreditUsageTracker
}) => {
    return tool({
        description: 'Get complete file content or list directory contents when queryCodebase snippets are insufficient. Use sparingly - only when you need to see the full file structure, all imports/exports, or complete implementation details that snippets miss.',
        parameters: z.object({
            path: z.string().describe("File or directory path to retrieve. For files: must match exactly a file from the project. For directories: use folder path like 'components' or 'screens' to list contents. Use '*' to see all top-level directories and files."),
            lineRange: z.array(z.number()).length(2).optional().describe("Optional [start, end] line range (1-based, inclusive). Use [start, -1] for start to end of file. Only applicable for files."),
            searchPattern: z.string().optional().describe("Optional regex pattern to search within the file. Shows matching lines with context. Only applicable for files."),
            reason: z.string().describe("Why you need the full file content or directory listing instead of using queryCodebase snippets.")
        }),
        execute: async ({path, lineRange, searchPattern, reason}: {
            path: string,
            lineRange?: [number, number],
            searchPattern?: string,
            reason: string
        }) => {
            try {
                const files = fileManager.getFileItems();
                // Normalize path - handle both /libs and libs formats
                const normalizedPath = path.startsWith('/') ? path.slice(1) : path;

                // Check if this is a directory listing request
                const isDirectoryRequest = !normalizedPath.includes('.') || normalizedPath.endsWith('/') || normalizedPath === '*';

                if (isDirectoryRequest) {
                    // Directory listing
                    const dirPath = normalizedPath === '*' ? '' : normalizedPath.replace(/\/$/, ''); // Remove trailing slash

                    // Find all files that start with this directory path
                    const matchingFiles = files.filter(f => {
                        const fileName = f.name.startsWith('/') ? f.name.slice(1) : f.name;
                        if (dirPath === '') {
                            // For root or * pattern, include all files
                            return true;
                        }
                        return fileName.startsWith(dirPath + '/');
                    });

                    if (matchingFiles.length === 0) {
                        // Show available top-level directories
                        const topLevelDirs = new Set<string>();
                        files.forEach(f => {
                            const fileName = f.name.startsWith('/') ? f.name.slice(1) : f.name;
                            const firstSlash = fileName.indexOf('/');
                            if (firstSlash > 0) {
                                topLevelDirs.add(fileName.substring(0, firstSlash));
                            }
                        });

                        return `Directory ${path} not found. Available directories: ${Array.from(topLevelDirs).join(', ')}`;
                    }

                    // Group files by subdirectory with depth 2 support
                    const structure = new Map<string, { files: string[], subdirs: Map<string, string[]> }>();

                    matchingFiles.forEach(f => {
                        const fileName = f.name.startsWith('/') ? f.name.slice(1) : f.name;
                        const relativePath = fileName.substring(dirPath.length + (dirPath ? 1 : 0));
                        const pathParts = relativePath.split('/');

                        if (pathParts.length === 1) {
                            // Direct file in this directory
                            if (!structure.has('.')) structure.set('.', { files: [], subdirs: new Map() });
                            structure.get('.')!.files.push(pathParts[0]);
                        } else if (pathParts.length === 2) {
                            // File in immediate subdirectory (depth 1)
                            const subDir = pathParts[0];
                            if (!structure.has(subDir)) structure.set(subDir, { files: [], subdirs: new Map() });
                            structure.get(subDir)!.files.push(pathParts[1]);
                        } else if (pathParts.length >= 3) {
                            // File in nested subdirectory (depth 2+)
                            const subDir = pathParts[0];
                            const nestedSubDir = pathParts[1];
                            if (!structure.has(subDir)) structure.set(subDir, { files: [], subdirs: new Map() });

                            const subDirData = structure.get(subDir)!;
                            if (!subDirData.subdirs.has(nestedSubDir)) {
                                subDirData.subdirs.set(nestedSubDir, []);
                            }
                            subDirData.subdirs.get(nestedSubDir)!.push(pathParts.slice(2).join('/'));
                        }
                    });

                    // Format directory listing
                    const displayPath = path === '*' ? 'Project Root' : path;
                    let listing = `Directory: ${displayPath} (${matchingFiles.length} items)\n\n`;

                    // Show subdirectories first
                    const sortedKeys = Array.from(structure.keys()).sort((a, b) => {
                        if (a === '.') return 1; // Files last
                        if (b === '.') return -1;
                        return a.localeCompare(b);
                    });

                    sortedKeys.forEach(key => {
                        if (key === '.') {
                            // Direct files
                            const directFiles = structure.get(key)!.files.sort();
                            directFiles.forEach(file => {
                                const fullPath = dirPath ? `${dirPath}/${file}` : file;
                                listing += `📄 ${file} (${fullPath})\n`;
                            });
                        } else {
                            // Subdirectory
                            const subDirData = structure.get(key)!;
                            const subDirPath = dirPath ? `${dirPath}/${key}` : key;
                            const totalFiles = subDirData.files.length +
                                Array.from(subDirData.subdirs.values()).reduce((sum, files) => sum + files.length, 0);

                            listing += `📁 ${key}/ (${totalFiles} files) - ${subDirPath}/\n`;

                            // Show files in this subdirectory
                            subDirData.files.sort().forEach(file => {
                                listing += `  📄 ${file} (${subDirPath}/${file})\n`;
                            });

                            // Show nested subdirectories (depth 2)
                            const sortedSubDirs = Array.from(subDirData.subdirs.keys()).sort();
                            sortedSubDirs.forEach(nestedDir => {
                                const nestedFiles = subDirData.subdirs.get(nestedDir)!;
                                const nestedPath = `${subDirPath}/${nestedDir}`;
                                listing += `  📁 ${nestedDir}/ (${nestedFiles.length} files) - ${nestedPath}/\n`;

                                // Show first few files in nested directory
                                const filesToShow = nestedFiles.slice(0, 3);
                                filesToShow.forEach(file => {
                                    listing += `    📄 ${file} (${nestedPath}/${file})\n`;
                                });

                                if (nestedFiles.length > 3) {
                                    listing += `    ... and ${nestedFiles.length - 3} more files\n`;
                                }
                            });
                        }
                    });

                    return listing;
                }

                // File content request
                const fileItem = files.find(f => f.name === normalizedPath || f.name === `/${normalizedPath}`);

                if (!fileItem) {
                    return `File ${path} not found. Available files: ${files.slice(0, 10).map(f => f.name).join(', ')}${files.length > 10 ? '...' : ''}`;
                }

                let content = fileItem.content;
                const lines = content.split('\n');
                const totalLines = lines.length;

                // Apply search pattern if specified
                if (searchPattern) {
                    try {
                        const regex = new RegExp(searchPattern, 'gi');
                        const matchingLines: string[] = [];
                        const contextBefore = 3;
                        const contextAfter = 3;

                        let lastIncludedLine = -1;

                        lines.forEach((line, index) => {
                            if (regex.test(line)) {
                                const startContext = Math.max(0, index - contextBefore);
                                const endContext = Math.min(lines.length - 1, index + contextAfter);

                                // Add gap indicator if there's a gap
                                if (startContext > lastIncludedLine + 1) {
                                    matchingLines.push('...');
                                }

                                // Add context and matching line
                                for (let i = startContext; i <= endContext; i++) {
                                    if (i > lastIncludedLine) {
                                        const lineNumber = i + 1;
                                        const prefix = i === index ? '>' : ' ';
                                        matchingLines.push(`${prefix}${lineNumber.toString().padStart(4)}: ${lines[i]}`);
                                        lastIncludedLine = i;
                                    }
                                }
                            }
                        });

                        content = matchingLines.join('\n');
                    } catch (regexError) {
                        return `Invalid regex pattern: ${searchPattern}`;
                    }
                } else if (lineRange) {
                    // Apply line range if specified
                    const [start, end] = lineRange;
                    const startLine = Math.max(1, start) - 1; // Convert to 0-based
                    const endLine = end === -1 ? lines.length : Math.min(end, lines.length);

                    const selectedLines = lines.slice(startLine, endLine);
                    content = selectedLines.map((line, index) => {
                        const lineNumber = startLine + index + 1;
                        return `${lineNumber.toString().padStart(4)}: ${line}`;
                    }).join('\n');
                } else {
                    // Return full file with line numbers
                    content = lines.map((line, index) => {
                        const lineNumber = index + 1;
                        return `${lineNumber.toString().padStart(4)}: ${line}`;
                    }).join('\n');
                }

                creditUsageTracker.trackOperation('tool_call');

                const resultLines = content.split('\n').length;
                const truncated = resultLines > 500;

                if (truncated) {
                    const truncatedLines = content.split('\n').slice(0, 500);
                    content = truncatedLines.join('\n') + '\n\n<response clipped - file too long>';
                }

                return `File: ${path} (${totalLines} lines total, showing ${truncated ? '500' : resultLines} lines)
Reason: ${reason}
${lineRange ? `Line range: ${lineRange[0]}-${lineRange[1] === -1 ? 'end' : lineRange[1]}` : ''}
${searchPattern ? `Search pattern: ${searchPattern}` : ''}

${content}`;

            } catch (error: any) {
                console.error('Error in getFileContents:', error);
                return `Error retrieving file content: ${error.message}`;
            }
        }
    });
};