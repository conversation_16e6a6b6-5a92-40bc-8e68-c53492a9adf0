import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getLatestDiscussChatByProjectId, getDiscussChatsByProjectId, getProjectById } from '@/lib/db/queries';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    const session = await auth();
    const anonymousId = request.headers.get('x-anonymous-id');
    const userId = session?.user.id || anonymousId;

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { projectId } = await params;
    const { searchParams } = new URL(request.url);
    const latest = searchParams.get('latest') === 'true';

    // Get the project to verify ownership
    const project = await getProjectById({ id: projectId });

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    // Check if user owns the project
    if (project.userId !== userId && session?.user?.email !== "<EMAIL>") {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    if (latest) {
      // Get the latest discuss chat for this project (backward compatibility)
      const discussChat = await getLatestDiscussChatByProjectId({ id: projectId });
      return NextResponse.json({ discussChat });
    } else {
      // Get all discuss chats for this project
      const discussChats = await getDiscussChatsByProjectId({ id: projectId });
      return NextResponse.json({ discussChats });
    }
  } catch (error) {
    console.error('Error fetching discuss chats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch discuss chats' },
      { status: 500 }
    );
  }
}
