import {
    CoreMessage,
    createDataStreamResponse,
    streamText,
    smoothStream,
    DataStreamWriter, TextPart,
} from 'ai';
import {customModel} from "@/lib/ai";
import {generateUUID} from "@/lib/utils";
import {StreamService} from "@/lib/services/stream-service";
import {MOFileParser} from "@/lib/parser/StreamParser";
import {MODiffParser} from "@/lib/parser/DiffParser";
import {FileNode} from "@/types/file";
import dayjs from "dayjs";
import {getChatById} from "@/lib/db/queries";
import {getProjectById} from "@/lib/db/project-queries";
import {Project} from "@/lib/db/schema";
import {getSupabaseInstructions} from "@/lib/chat/tools/get-supabase-instructions";
import {getSupabaseLogs} from "@/lib/chat/tools/get-supabase-logs";
import {CreditUsageTracker} from "@/lib/credit/CreditUsageTracker";
import { KNOWN_ERRORS } from '@/lib/ai/known-errors';
import {queryCodebase} from "@/lib/chat/tools";
import {FileLineManager} from "@/lib/editor/FileLineManager";
import {Extractor} from "@/lib/chat/handlers/file-message/extractor";

/**
 * @deprecated This endpoint is deprecated. Use /api/chat with isDiscussion: true instead.
 * Simple API for discussing with AI without saving to database
 * This can be used for error fixing, getting a second opinion, or any quick discussion
 */
export async function POST(request: Request) {
    console.warn('DEPRECATED: /api/discuss endpoint is deprecated. Use /api/chat with isDiscussion: true instead.');
    try {
        // Parse the request body
        const body = await request.json();

        // Extract data from the useChat hook format
        const messages = body.messages || [];
        const files = body.files || [];
        const logs = body.logs || [];
        const projectId = body.projectId;
        const messageCount = body.messageCount || 0; // Track message count for caching
        if(!projectId) {
            return new Response('Need a projectId to continue', { status: 404 });
        }

        let [project] = await Promise.all([
            projectId ? getProjectById({id: projectId}) : null
        ]) as [Project];

        if(!project) {
            return new Response('Need a valid project to continue', { status: 404 });
        }

        let isSupabaseConnected = !!(project.supabaseProjectId && project.connectionId);

        // Create a unique message ID for this discussion
        const messageId = generateUUID();

        return createDataStreamResponse({
            execute: async (dataStream) => {
                // Create a standard system message for all discussions
                const systemMessage: CoreMessage = {
                    role: 'system',
                    content: `You are a helpful AI assistant specialized in helping with code and technical issues. 
You are currently working in magically, an AI powered mobile app building tool where AI writes code in React Native Expo and runs in Expo Snack and Expo Go.
magically is an AI which might need your assistance so always be aware that when people are talking to you, they might be taking second opinions to help devise a strategy to build their app, fix errors or generally understand.
The end goal of your conversation with the user is to finally compile a plan to send to magically. Always remember that and build towards it.
You're currently in a quick discussion mode to provide a second opinion or help fix errors.
Be concise, specific, and focus on providing actionable solutions.
You are talking to a non-technical user. DO NOT write code to explain to the user. Talk about the Root cause instead of the symptom. Please talk in a supportive tone and keep it concise. ASK if they want a plan to be given to magically to fix/progress.

Dont' use the tool 'getSupabaseInstructions' to fetch console logs. Use it judiciously to fetch any database schema, triggers, edge functions, secrets, triggers, database functions. It will not give you logs, yet.

${isSupabaseConnected ? 'Supabase for this project is connected. You can use the tool getSupabaseInstructions to fetch more details about the project': ''}

<boundary_conditions>
  ⚠️ CRITICAL constraints:

  • Environment: Expo web preview, use only available dependencies, no config file access, assets only via API
  • Token: LLM gets only last 5 messages, be concise, avoid excessive code
  • Users: Non-technical, no jargon, focus on business value
  • Supabase: Unique storage keys, proper error handling, RLS policies, Edge Functions for sensitive ops
  • Tools: ONE comprehensive queryCodebase call, getSupabaseInstructions when connected
  • Security: NEVER embed secrets in code, use Edge Functions for sensitive credentials
  • File Size: NEVER create files >250-300 lines, break down larger files into smaller, focused modules
  • Code Organization: Single-purpose files, consistent naming, add @magic_description comments
  • Navigation: NEVER leave buttons/components without navigation if corresponding screens exist

  ⚠️ Expo Snack Compatibility:
  • NEVER add code that is unsupported in Expo Snack, even if user requests it
  • ALWAYS verify imports are compatible with Expo web environment
  • Use platform-specific code with Platform.select() when needed
  • For native-only features, provide web fallbacks or graceful degradation
  • NEVER use features requiring native modules without web alternatives
  • AVOID ScrollView nesting that causes scrolling issues
  • PREVENT excessive re-renders that slow down Expo Snack
  • USE FlatList with proper key extraction for lists
  • IMPLEMENT proper keyboard handling for form inputs
  • OPTIMIZE image loading and rendering for performance

  <allowed_code>
    • Expo, React Native, Supabase, TypeScript ONLY
    • Database migrations only in MO_DATABASE_QUERY when Supabase connected
    • Dummy JSON files
    • App.tsx must be present and compatible with Expo
  </allowed_code>

  <not_allowed_code>
    • Sample images, fonts, audio files
    • Backend code of any format
    • JavaScript (.js) files (use TypeScript only)
    • Configuration files (babel.config, metro.config, package.json, app.json)
    • Non-Expo/React Native code
    • SQL files
    • Embedding sensitive keys (use Supabase Edge Functions)
  </not_allowed_code>
</boundary_conditions>

${KNOWN_ERRORS}
`
                };

                // Start with the system message and add all messages from the client
                // The client already formats the messages appropriately based on type

                let fileMessage = '';
                // Add file context if files are provided
                if (files.length > 0) {
                    // Create an extractor instance for minimal file structure
                    const extractor = new Extractor();
                    
                    // Create file message content with minimal structure to reduce token usage
                    fileMessage = `<FILE_MESSAGE>
Given the following files:

${files.map((file: FileNode) => {
                        const fileCount = extractor.getFileLineCount(file.content);
                        const warnings: string[] = [];
                        
                        if(fileCount.warning) {
                            warnings.push(fileCount.warning);
                        }
                        
                        return `
---- File: ------------
Path: ${file.name}
FileType: ${extractor.getFileType(file.name)}
Number of lines: ${fileCount.count}
Warnings to solve: ${warnings.join(',')}
<MO_FILE path="${file.name}" type="${extractor.getFileType(file.name)}" lines="${fileCount.count}" warnings="${warnings.join(',')}">
${extractor.extractMinimalFileStructure(file.content)}
</MO_FILE>
`;
                    }).join('')}
${extractor.generateMinimalDependencyGraph(files)}

Please analyze these files along with the message to provide a solution.
Use the queryCodebase tool if you need more detailed information about specific files.
</FILE_MESSAGE>`;
                }

                // Process logs separately to avoid breaking cache
                let logsMessage = '';
                let logsSystemMessage: CoreMessage | undefined;
                
                if(logs && logs.length) {
                    // Create detailed log message
                    logsMessage = `Here are the logs from the execution environment (expo app console) sorted by oldest to the latest along with the type: 
        ${logs.map((log: any) => {
                        return `Timestamp: ${dayjs(log.timestamp).toISOString()} | Source: ${log.source} - [${log.type}]: ${log.message}`;
                    }).join('\n')}`;
                    
                    // Create a separate system message for logs with cache control set to never cache
                    // This ensures logs don't break the LLM caching for the rest of the conversation
                    logsSystemMessage = {
                        role: 'system',
                        content: logsMessage,
                        providerOptions: {
                            openrouter: {
                                cacheControl: { type: 'no-cache' },
                            },
                        },
                        experimental_providerMetadata: {
                            openrouter: {
                                cacheControl: { type: 'no-cache' },
                            },
                        },
                    };
                    
                    // Replace detailed logs with a brief notification in the main message flow
                    logsMessage = 'Logs are available. Check the separate logs message for details.';
                }

                // Initialize parsers
                const fileParser = new MOFileParser();
                const diffParser = new MODiffParser();
                const creditUsageTracker = new CreditUsageTracker();
                const fileManager = new FileLineManager();
                fileManager.initializeFiles(files);

                const userMessageId = generateUUID();

                const enabledTools: string[] = ['queryCodebase'];
                if (isSupabaseConnected) {
                    enabledTools.push('getSupabaseInstructions')
                    enabledTools.push('getSupabaseLogs')
                }
                // Implement message caching strategy
                // Keep a consistent set of messages for 5 turns before refreshing
                // This optimizes LLM caching by reducing cache misses
                const MIN_MESSAGES = 8;
                const MAX_MESSAGES = 12;
                const MOD_TURNS = 4;
                
                // Process messages for caching optimization
                let processedMessages = [...messages];
                
                // If we have more than MIN_MESSAGES, apply the modulo strategy
                if (messages.length > MIN_MESSAGES) {
                    // Determine if we should trim messages based on message count modulo
                    const shouldTrim = messageCount % MOD_TURNS === 0;
                    
                    if (shouldTrim || messages.length > MAX_MESSAGES) {
                        // Trim down to MIN_MESSAGES to refresh the cache
                        processedMessages = messages.slice(-MIN_MESSAGES);
                        console.log(`[Message:${messageId}] Trimming messages to ${MIN_MESSAGES} for cache refresh`);
                    } else {
                        // Keep the same message set to leverage cache
                        // But don't let it grow beyond MAX_MESSAGES
                        processedMessages = messages.slice(-Math.min(messages.length, MAX_MESSAGES));
                        console.log(`[Message:${messageId}] Maintaining message set for cache hit, count: ${processedMessages.length}`);
                    }
                }
                
                // Create stream service
                const streamService = new StreamService({
                    messages: [
                        systemMessage,
                        {
                            content: fileMessage,
                            role: 'system'
                        },
                        // Include a brief log notification in the cached message flow
                        logsMessage ? {
                            role: 'system',
                            content: 'Console logs are available. Use them to help diagnose any issues.'
                        } : undefined,
                        ...processedMessages,
                        // Add the detailed logs as a separate non-cached message if available
                        ...(logsSystemMessage ? [logsSystemMessage] : []),
                    ],
                    parser: fileParser,
                    diffParser: diffParser,
                    dataStream,
                    userMessageId: messageId,
                    temperature: 0,
                    modelId: "google/gemini-2.5-pro-preview",
                    tools: {
                        getSupabaseInstructions: getSupabaseInstructions({
                            dataStream,
                            creditUsageTracker,
                            project
                        }),
                        getSupabaseLogs: getSupabaseLogs({
                            dataStream,
                            creditUsageTracker,
                            project
                        }),
                        queryCodebase: queryCodebase({
                            projectId,
                            fileManager,
                            files,
                            messageId: userMessageId,
                            userMessage: '',
                        }),
                    },
                    transformChunking: "word",
                    enabledTools,
                    isSupabaseConnected,
                    maxSteps: 5,

                    onFinish: async ({finishReason}) => {
                        // The useChat hook doesn't need a special completion event
                        // as it handles the standard AI response format automatically
                    },
                });

                // Start streaming
                const result = await streamService.startStream();
                result.mergeIntoDataStream(dataStream, {sendUsage: false, sendReasoning: false});
            }
        });


    } catch (error: any) {
        console.error('Error in discuss API:', error);
        return new Response(JSON.stringify({error: error.message}), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}
