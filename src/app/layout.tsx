import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>eist, <PERSON>eist_Mono, Berkshire_Swash } from "next/font/google";
import "./globals.css";
import "@/styles/safe-area.css";
import { ThemeProvider } from "@/components/theme-provider";
import { MainLayout } from "@/components/layout/main-layout";
import { Toaster } from "sonner";
import {SessionProvider} from "next-auth/react";
import {CSPostHogProvider} from "@/providers/posthog-provider";
import { Analytics } from "@vercel/analytics/react"
import { StoreWrapper } from "@/stores/utils/StoreContext";
import {UpgradeDialog} from "@/components/upgrade-dialog";
import {LoginDialog} from "@/components/login-dialog";
import {AnonymousProvider} from "@/providers/anonymous-provider";
import Notifications from "@/components/Notifications";
import Script from "next/script";
import {GoogleTagManager} from "@next/third-parties/google";
import FeaturebaseMessenger from "@/components/base/FeaturebaseMessenger";

export const experimental_ppr = true;

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const logoFont = Berkshire_Swash({
  variable: "--font-berkshire-swash",
  subsets: ["latin"],
  weight: ["400"]
});

export const metadata: Metadata = {
  metadataBase: new URL('https://magically.life'),
  title: "magically.life - Build Android and iOS Apps Magically with AI",
  description: "Android, iOS mobile apps in minutes. Now with support for one-click push to app stores, supabase, live updates and so much more.",
  keywords: ["app builder", "no-code", "AI", "mobile apps", "React Native", "development"],
  icons: {
    icon: [
      { url: '/favicon.ico' },
      { url: '/android-chrome-192x192.png', type: 'image/png', sizes: '192x192' }
    ],
    apple: {
      url: '/apple-touch-icon.png',
      sizes: '180x180',
      type: 'image/png'
    }
  },
  openGraph: {
    title: "magically.life - Build Android and iOS Apps Magically with AI",
    description: "Android, iOS mobile apps in minutes. Now with support for one-click push to app stores, supabase, live updates and so much more.",
    url: "https://magically.life",
    siteName: "magically.life",
    images: [
      {
        url: "https://magically.life/og-image.jpg",
        width: 512,
        height: 512,
      },
    ],
    locale: "en-US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "magically.life - Build Android and iOS Apps Magically with AI",
    description: "Android, iOS mobile apps in minutes. Now with support for one-click push to app stores, supabase, live updates and so much more.",
    images: ["https://magically.life/og-image.jpg"],
  },
};

export default async function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script async src="https://cdn.tolt.io/tolt.js" data-tolt="pk_yGKHbsFGafM3JK2eeHauZ9si"></script>
        <Script id="lemon-squeezy-config" strategy="beforeInteractive">
          {`window.lemonSqueezyAffiliateConfig = { store: "magically-life" };`}
        </Script>
        <Script
            src="https://lmsqueezy.com/affiliate.js"
            strategy="lazyOnload"
            id="lemon-squeezy-script"
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${logoFont.variable} min-h-dvh bg-background font-sans antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          disableTransitionOnChange
        >
          <Analytics/>
          <GoogleTagManager gtmId="AW-17019881839" />
          <SessionProvider>
            <CSPostHogProvider>
              <StoreWrapper>
                <AnonymousProvider>
                    {/*<FeaturebaseMessenger/>*/}
                    <MainLayout>{children}</MainLayout>
                    <UpgradeDialog/>
                    <LoginDialog/>
                    <Notifications/>
                </AnonymousProvider>
              </StoreWrapper>
            </CSPostHogProvider>
          </SessionProvider>
          <Toaster position="top-center" theme="system" closeButton richColors />
        </ThemeProvider>
      </body>
    </html>
  );
}
