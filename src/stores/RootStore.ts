import {action} from "mobx"
import {GeneratorStore} from "@/stores/GeneratorStore";
import {SnackStore} from "@/stores/SnackStore";
import { IntegrationStore } from "@/stores/IntegrationStore";
import { LogStore } from "@/stores/LogStore";
import { TestFlightStore } from "@/stores/TestFlightStore";
import { DeploymentStore } from "@/stores/DeploymentStore";
import { NotificationStore } from "@/stores/NotificationStore";
import { DesignPreviewStore } from "@/stores/DesignPreviewStore";
import { SupabaseStore } from "@/stores/SupabaseStore";
import { DesignStore } from "@/stores/DesignStore";
import { AppStoreReviewStore } from "@/stores/AppStoreReviewStore";
import { DiscussStore } from "@/stores/DiscussStore";

export type PropsWithStore<T> = T & {
    rootStore?: RootStore;
};

export class RootStore {
    stores: any[]
    generatorStore: GeneratorStore
    snackStore: SnackStore
    integrationStore: IntegrationStore
    logStore: LogStore
    testFlightStore: TestFlightStore
    deploymentStore: DeploymentStore
    notificationStore: NotificationStore
    designPreviewStore: DesignPreviewStore
    supabaseStore: SupabaseStore
    designStore: DesignStore
    appStoreReviewStore: AppStoreReviewStore
    discussStore: DiscussStore


    constructor() {
        this.generatorStore = new GeneratorStore(this)
        this.snackStore = new SnackStore(this)
        this.integrationStore = new IntegrationStore(this)
        this.logStore = new LogStore(this)
        this.testFlightStore = new TestFlightStore(this)
        this.deploymentStore = new DeploymentStore(this)
        this.notificationStore = new NotificationStore(this)
        this.designPreviewStore = new DesignPreviewStore(this)
        this.supabaseStore = new SupabaseStore(this)
        this.designStore = new DesignStore(this)
        this.appStoreReviewStore = new AppStoreReviewStore(this)
        this.discussStore = new DiscussStore(this)
        this.stores = [
            this.generatorStore,
            this.snackStore,
            this.integrationStore,
            this.logStore,
            this.testFlightStore,
            this.deploymentStore,
            this.notificationStore,
            this.designPreviewStore,
            this.supabaseStore,
            this.designStore,
            this.appStoreReviewStore,
            this.discussStore
        ]
    }

    @action
    onLogin(user: any): any {
        this.stores?.forEach((instance) =>
            this.executor(instance, 'onLogin', user)
        );
        // this.hotspotsStore.onLogin(user);
        // this.feedStore.onLogin(user);
        // this.notificationsStore.onLogin(user);
    }

    @action
    onLogout(user: any): any {
        this.stores.forEach((instance) =>
            this.executor(instance, 'onLogout', user)
        );
        // this.hotspotsStore.onLogout(user);
        // this.feedStore.onLogout(user);
        // this.notificationsStore.onLogout(user);
    }

    @action
    onBrokerDeLink(): any {
        // this.stores.forEach((instance) =>
        //   this.executor(instance, "onBrokerDeLink"),
        // );
        // captureEvent("BrokerDelinkSuccess");
    }

    @action
    onBrokerLink(): any {
        // this.stores.forEach((instance) => this.executor(instance, "onBrokerLink"));
        // captureEvent("BrokerLoginComplete");
        // this.hotspotsStore.onBrokerLink();
        // this.notificationsStore.onBrokerDeLink();
        // this.feedStore.onBrokerLink();
    }

    @action
    onNotificationShownInForeground(action: string) {
        // this.stores.forEach((instance) => {
        //   this.executor(instance, "onNotificationShownInForeground", action);
        // });
        // if (action && action === "delink") {
        //   this.onBrokerDeLink();
        // }
    }

    @action
    onEventFired(eventParams: { event: string; params: any }) {
        // this.stores.forEach((instance) => {
        //   this.executor(instance, "onEventFired",  eventParams);
        // });
    }

    @action
    executor(instance: any, fn: string, args?: any) {
        if (instance && instance[fn] && typeof instance[fn] === 'function') {
            try {
                instance[fn](args);
            } catch (e) {
                console.log('Caught error with executor', e);
            }
        }
    }
}

export default new RootStore();
