import {action, computed, makeAutoObservable, observable, runInAction} from "mobx";
import {Message} from "@/lib/db/schema";
import {CodeBlock, FileItem, FileNode} from "@/types/file";
import {DEFAULT_DEPENDENCIES} from "@/types/editor";
import {convertToFileTree} from "@/utils/file-tree-converter";
import {migrateToV2} from "@/utils/file-converter";
import {RootStore} from "@/stores/RootStore";
import {ChatRequestOptions, UIMessage} from "@ai-sdk/ui-utils";
import {CoreMessage, CreateMessage} from "ai";
import {toast} from "sonner";
import {ComponentContext, ComponentContextAttachment} from "@/types/component-context";
import {base64ToFile, uploadFile} from "@/services/Uploads";
import {generateUUID} from "@/lib/utils";
import * as Sentry from "@sentry/nextjs";

// Import patterns for dependency detection
const IMPORT_REGEX = {
  // ES6 imports: import X from 'package' or import X, { Y } from 'package'
  es6: /import\s+(?:(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+\w+|\w+))?\s+from\s+)?['"]([^'"]+)['"]\s*/g,
  // require: const X = require('package')
  commonjs: /(?:const|let|var)\s+(?:\w+|\{[^}]*\})\s*=\s*require\s*\(\s*['"]([^'"]+)['"]\s*\)/g,
  // dynamic import: import('package')
  dynamic: /import\s*\(\s*['"]([^'"]+)['"]\s*\)/g,
};

// Map of package paths to actual npm package names for special cases
const PACKAGE_MAPPING: Record<string, string> = {
  'react': 'react',
  'react-native': 'react-native',
  'expo': 'expo',
  'expo/vector-icons': 'expo',
  '@expo/vector-icons': '@expo/vector-icons',
};

export type SnackError = {
    message: string;
    timestamp: number;
    handled: boolean;
    type?: 'default'| 'supabase'
}

export type ProjectSessionState = {
    status: 'idle' | 'loading' | 'streaming' | 'error';
    view: 'code' | 'preview';
    previewDialogOpen: boolean;
}

export type ClientTestingState = {
    isActive: boolean;
    toolCallId: string | null;
    featuresToTest: string;
    expectations: string;
    reason: string;
    startTime: number;
    endTime: number | null;
}

export type FileOperation = {
    type: 'create' | 'edit' | 'delete';
    status: 'pending' | 'complete' | 'error';
    path: string;
    timestamp: number;
}

export type ProgressState = {
    status: string;  // User-friendly messages
    percent: number;
    currentOperation?: string;
}

export class ProjectSession {
    private readonly rootStore: RootStore;
    // Core State
    @observable id: string;
    @observable messages: Message[] = [];
    @observable state: ProjectSessionState = {
        status: 'idle',
        view: 'preview',
        previewDialogOpen: false
    };
    @observable secretsStatus: 'idle' | 'saving' | 'error' = 'idle';
    @observable currentMessage: string = '';
    @observable currentFile: File | null = null;
    @observable isInitial = false;
    @observable projectId: string | null = null;
    @observable needsContinuation: boolean = false;

    // Component Context
    @observable componentContexts: ComponentContext[] = [];
    @observable componentContextUploading: boolean = false;

    // File Management
    @observable fileTree: FileItem[] = [];
    @observable activeFile: FileNode | null = null;
    @observable dependencies: Record<string, { version: string }> = DEFAULT_DEPENDENCIES;
    @observable fileOperations = new Map<string, FileOperation>();

    // UI State
    @observable previewUrl?: string;
    @observable errors: Error[] = [];
    @observable snackError: SnackError | null = null;
    @observable tabSelection: 'preview' | 'code' | 'terminal' | 'supabase' = 'preview'
    @observable chatLoading = false;
    @observable initialPrompt = '';
    @observable fullScreen = false;
    @observable hasStartedStreaming = false;
    @observable changesWhileInactive = false;

    // Progress State
    @observable progress: ProgressState = {
        status: "Ready to start...",
        percent: 0
    };

    @observable isViewingHistoricalVersion = false;
    @observable historicalMessageId: string | null = null;
    @observable isLoadingHistoricalVersion = false;
    @observable updatedUserMessage = '';

    @observable postStreamAnalysis: {analysis: string, message: string} = {
        analysis: '',
        message: ''
    }
    @observable postStreamAnalysisStatus: 'idle' | 'loading' | 'loaded' | 'errored' = 'idle';
    
    // Client Testing State
    @observable clientTesting: ClientTestingState = {
        isActive: false,
        toolCallId: null,
        featuresToTest: '',
        expectations: '',
        reason: '',
        startTime: 0,
        endTime: null
    };

    constructor(rootStore: RootStore, id: string, initialState?: Partial<ProjectSession>) {
        this.id = id;
        if (initialState) {
            Object.assign(this, initialState);
        }
        console.log('Init session', id)
        makeAutoObservable(this);
        this.rootStore = rootStore;
    }

    @action
    async setStatus(status: ProjectSessionState['status']) {
        if(this.state.status === "streaming" && status === "idle") {
            await this.rootStore.snackStore.updateFiles(this.id, this.fileTree, this.dependencies)
        }
        this.state.status = status;
    }

    @action
    setView(view: ProjectSessionState['view']) {
        this.state.view = view;
    }

    @action
    setDependencies(dependencies: Record<string, { version: string }>) {
        this.dependencies = dependencies;
        console.log('Dependencies updated in ProjectSessionStore:', dependencies);
    }
    
    /**
     * Adds a new dependency to the project
     * @param packageName Name of the npm package to add
     * @param version Version of the package (defaults to '*')
     * @returns Promise resolving to true if successful, false otherwise
     */
    @action
    async addDependency(packageName: string, version: string = '*'): Promise<boolean> {
        try {
            console.log(`Adding dependency ${packageName}@${version}`);
            
            // Create updated dependencies object
            const updatedDependencies = { 
                ...this.dependencies,
                [packageName]: { version }
            };
            
            // Update dependencies in the session
            this.setDependencies(updatedDependencies);
            
            // Update Snack with the new dependencies
            await this.rootStore.snackStore.updateFiles(this.id, this.fileTree, updatedDependencies);
            
            // Save to API if project ID exists
            if (this.projectId) {
                await this.saveToAPI();
            }
            
            console.log(`Successfully added dependency ${packageName}@${version}`);
            return true;
        } catch (error) {
            console.error(`Error adding dependency ${packageName}:`, error);
            return false;
        }
    }
    
    /**
     * Removes a dependency from the project
     * @param packageName Name of the npm package to remove
     * @returns Promise resolving to true if successful, false otherwise
     */
    @action
    async removeDependency(packageName: string): Promise<boolean> {
        try {
            console.log(`Removing dependency ${packageName}`);
            
            // Check if the dependency exists
            if (!this.dependencies[packageName]) {
                console.warn(`Dependency ${packageName} not found in project`);
                return false;
            }
            
            // Create a new dependencies object without the removed package
            const { [packageName]: removed, ...remainingDependencies } = this.dependencies;
            
            // Update dependencies in the session
            this.setDependencies(remainingDependencies);
            
            // Update Snack with the updated dependencies
            await this.rootStore.snackStore.updateFiles(this.id, this.fileTree, remainingDependencies);
            
            // Save to API if project ID exists
            if (this.projectId) {
                await this.saveToAPI();
            }
            
            console.log(`Successfully removed dependency ${packageName}`);
            return true;
        } catch (error) {
            console.error(`Error removing dependency ${packageName}:`, error);
            return false;
        }
    }
    
    /**
     * Detects dependencies from code files and updates the dependencies in the session
     * @param files Array of files to scan for dependencies
     */
    @action
    detectAndUpdateDependencies(files: FileItem[]) {
        try {
            console.log('Detecting dependencies from files:', files.length);
            
            // Detect dependencies from code
            const detectedDependencies = this.detectDependenciesFromCode(files);
            console.log('Detected dependencies:', detectedDependencies);
            
            // Get current dependencies
            const currentDependencies = this.dependencies || {};
            
            // Create update object with only new dependencies
            const dependencyUpdates = this.createDependencyUpdateObject(currentDependencies, detectedDependencies);
            
            // Only update if there are new dependencies
            if (Object.keys(dependencyUpdates).length > 0) {
                console.log('New dependencies detected:', dependencyUpdates);
                
                // Update dependencies in the session
                const updatedDependencies = { ...currentDependencies };
                
                // Add new dependencies
                for (const [pkg, info] of Object.entries(dependencyUpdates)) {
                    if (info !== null) {
                        updatedDependencies[pkg] = info;
                    }
                }
                
                // Update the session dependencies
                this.setDependencies(updatedDependencies);
                
                // Save to API if project ID exists
                // if (this.projectId) {
                //     this.saveToAPI().catch(err => {
                //         console.error('Failed to save dependencies to API:', err);
                //     });
                // }
            }
        } catch (error) {
            console.error('Error detecting dependencies:', error);
        }
    }
    
    /**
     * Saves the current file tree and dependencies to the API
     */
    @action
    async saveToAPI() {
        if (!this.projectId) return null;
        
        try {
            const response = await fetch(`/api/project/${this.projectId}/files`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    files: this.fileTree,
                    dependencies: this.dependencies,
                    chatId: this.id
                })
            });
            
            if (!response.ok) {
                throw new Error(`API error: ${response.status}`);
            }
            
            const result = await response.json();
            console.log('Project state saved to API:', result);
            return result;
        } catch (error) {
            console.error('Error saving project state to API:', error);
            return null;
        }
    }
    
    /**
     * Detects dependencies from code files
     * @param files Array of file items to scan
     * @returns Record of dependencies
     */
    private detectDependenciesFromCode(files: FileItem[]): Record<string, { version: string }> {
        const dependencies: Record<string, { version: string }> = {};
        
        // Only process JS/TS files
        const jsFiles = files.filter(file => {
            const fileName = file.name.toLowerCase();
            return fileName.endsWith('.js') || 
                   fileName.endsWith('.jsx') || 
                   fileName.endsWith('.ts') || 
                   fileName.endsWith('.tsx');
        });
        
        for (const file of jsFiles) {
            const content = file.content;
            if (!content) continue;
            
            // Extract all imports using different patterns
            const extractImports = (regex: RegExp, content: string): string[] => {
                const matches: string[] = [];
                let match;
                while ((match = regex.exec(content)) !== null) {
                    if (match[1]) matches.push(match[1]);
                }
                return matches;
            };
            
            const imports = [
                ...extractImports(IMPORT_REGEX.es6, content),
                ...extractImports(IMPORT_REGEX.commonjs, content),
                ...extractImports(IMPORT_REGEX.dynamic, content),
            ];
            
            // Process imports to get package names
            for (let importPath of imports) {
                // Skip relative imports
                if (importPath.startsWith('./') || importPath.startsWith('../')) {
                    continue;
                }
                
                // Handle scoped packages and subpaths
                let packageName = importPath;
                
                // Extract the main package name from subpaths (e.g., 'lodash/map' -> 'lodash')
                if (!importPath.startsWith('@')) {
                    packageName = importPath.split('/')[0];
                } else {
                    // For scoped packages, get the scope + package name (e.g., '@expo/vector-icons')
                    const parts = importPath.split('/');
                    if (parts.length >= 2) {
                        packageName = `${parts[0]}/${parts[1]}`;
                    }
                }
                
                // Map to actual package name if needed
                const mappedPackage = PACKAGE_MAPPING[packageName] || packageName;
                
                // Skip React Native internal packages
                if (mappedPackage.startsWith('react-native/') || 
                    mappedPackage === 'react/jsx-runtime' ||
                    mappedPackage === 'react/jsx-dev-runtime') {
                    continue;
                }
                
                // Add to dependencies - accept any valid package name
                dependencies[mappedPackage] = { version: '*' };
            }
        }
        
        return dependencies;
    }
    
    /**
     * Creates a dependency update object by comparing current and detected dependencies
     */
    private createDependencyUpdateObject(
        currentDeps: Record<string, any>,
        detectedDeps: Record<string, { version: string }>
    ): Record<string, { version: string } | null> {
        const updates: Record<string, { version: string } | null> = {};
        
        // Add new dependencies
        for (const [pkg, info] of Object.entries(detectedDeps)) {
            if (!currentDeps[pkg]) {
                updates[pkg] = info;
            }
        }
        
        return updates;
    }

    @action
    setPreviewDialogOpen(open: boolean) {
        this.state.previewDialogOpen = open;
    }

    @action
    setProgress(progress: Partial<ProgressState>) {
        this.progress = {...this.progress, ...progress};
    }

    @action
    setFileTree(fileTree: FileItem[], dependencies?: Record<string, { version: string }> ) {
        if (dependencies) {
            this.dependencies = dependencies;
        }
        // Detect dependencies from the updated file tree
        // this.detectAndUpdateDependencies(fileTree);

        this.fileTree = fileTree;

        // Update the Snack with the new files and dependencies
        // this.rootStore.snackStore.updateFiles(this.id, fileTree, this.dependencies);
        this.pushUpdatesToPreview();
    }

    @action
    pushUpdatesToPreview() {
        this.rootStore.snackStore.updateFiles(this.id, this.fileTree, this.dependencies);
    }

    @action
    setActiveFile(file: FileNode | undefined) {
        if (file) {
            if(!this.fileTree.find(f => f.name === file.name)) {
                this.fileTree.push(file);
            }
            this.activeFile = file;
        }
    }

    @action
    setMessages(messages: Message[]) {
        this.messages = messages;
    }

    @action
    setCurrentTab(tabSelection: 'code' | 'preview' | 'terminal' | 'supabase') {
        this.tabSelection = tabSelection;
    }

    @action
    addMessage(message: Message) {
        this.messages.push(message);
    }

    @action
    setCurrentMessage(message: string) {
        this.currentMessage = message;
    }

    @action
    setCurrentFile = (currentFile: File) => {
        this.currentFile = currentFile;
    }
    
    @action
    setNeedsContinuation = (needsContinuation: boolean) => {
        this.needsContinuation = needsContinuation;
        console.log(`[ProjectSession] Setting needsContinuation to ${needsContinuation}`);
    }

    @action
    onUserMessageUpdate(userMessage: string) {
        this.updatedUserMessage  = userMessage;
    }
    
    @action
    startClientTesting(toolCallId: string, featuresToTest: string, expectations: string, reason: string) {
        console.log(`[ProjectSession] Starting client testing for tool call ${toolCallId}`);
        this.clientTesting = {
            isActive: true,
            toolCallId,
            featuresToTest,
            expectations,
            reason,
            startTime: Date.now(),
            endTime: null
        };
    }
    
    @action
    completeClientTesting(toolCallId: string) {
        console.log(`[ProjectSession] Completing client testing for tool call ${toolCallId}`);
        if (this.clientTesting.toolCallId === toolCallId) {
            this.clientTesting = {
                ...this.clientTesting,
                isActive: false,
                endTime: Date.now()
            };
            
            // Return the testing result to the AI
            return {
                result: "DONE",
                toolCallId
            };
        }
        return null;
    }
    
    @computed
    get isClientTestingActive() {
        return this.clientTesting.isActive;
    }
    
    @action
    addComponentContext(context: Omit<ComponentContext, 'id' | 'isUploading'>) {
        const id = `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
        
        // Create a new array to ensure observers detect the change
        const newContext = {
            ...context,
            id,
            isUploading: !!context.screenshot
        };
        
        // Replace the entire array to ensure MobX triggers updates
        this.componentContexts = [...this.componentContexts, newContext];

        // console.log('this.componentContexts', this.componentContexts)
        
        // Start uploading the screenshot
        this.uploadComponentScreenshot(id);
        
        return id;
    }
    
    @action
    removeComponentContext(id: string) {
        console.log('ProjectSessionStore: Removing component context with ID:', id);
        console.log('Before removal:', this.componentContexts.length, 'contexts');
        
        this.componentContexts = this.componentContexts.filter(context => context.id !== id);
        
        console.log('After removal:', this.componentContexts.length, 'contexts');
    }
    
    @action
    clearComponentContexts() {
        console.log('ProjectSessionStore: Clearing all component contexts');
        console.log('Before clearing:', this.componentContexts.length, 'contexts');
        
        // Create a new empty array to ensure observers detect the change
        this.componentContexts = [];
        
        console.log('After clearing:', this.componentContexts.length, 'contexts');
    }
    
    @action
    private async uploadComponentScreenshot(contextId: string) {
        const context = this.componentContexts.find(c => c.id === contextId);
        if (!context || !context.screenshot) return;
        
        try {
            // Convert base64 to file
            const file = base64ToFile(context.screenshot, `component-${context.sourceFile}-${context.componentName}.jpg`);
            
            // Upload the file
            const result = await uploadFile(file);
            
            if (result) {
                // Find the context and update it directly
                const index = this.componentContexts.findIndex(c => c.id === contextId);
                if (index !== -1) {
                    this.componentContexts[index].imageUrl = result.url;
                    this.componentContexts[index].isUploading = false;
                }
            } else {
                throw new Error('Upload failed');
            }
        } catch (error) {
            console.error('Error uploading component screenshot:', error);
            
            // Find the context and update it directly
            const index = this.componentContexts.findIndex(c => c.id === contextId);
            if (index !== -1) {
                this.componentContexts[index].isUploading = false;
                this.componentContexts[index].uploadError = error instanceof Error ? error.message : 'Unknown error';
            }
        }
    }
    
    @computed
    get isComponentContextUploading(): boolean {
        return this.componentContexts.some(context => context.isUploading);
    }
    
    @computed
    get componentContextAttachments(): ComponentContextAttachment[] {
        return this.componentContexts
            .filter(context => !context.isUploading && context.imageUrl)
            .map(context => ({
                name: `${context.componentName} (${context.sourceFile})`,
                url: context.imageUrl!,
                contentType: 'image/jpeg',
                componentContext: {
                    componentName: context.componentName,
                    element: context.element,
                    sourceFile: context.sourceFile,
                    lineNumber: context.lineNumber
                }
            }));
    }

    @action
    setChatLoading(chatLoading: boolean) {
        this.chatLoading = chatLoading;
    }

    @action
    setPreviewUrl(url: string) {
        this.previewUrl = url;
    }

    @action
    setInitialPrompt(initialPrompt: string) {
        this.initialPrompt = initialPrompt
    }

    @action
    toggleFullScreen() {
        this.fullScreen = !this.fullScreen;
    }

    @action
    setSnackError(message: string, type?: 'default'| 'supabase') {
        // Only set if different from current error or if current error was handled
        if (!this.snackError || this.snackError.handled || this.snackError.message !== message) {
            this.snackError = {
                message,
                timestamp: Date.now(),
                handled: false,
                type
            };
        }
    }

    @action
    markErrorAsHandled() {
        if (this.snackError) {
            this.snackError.handled = true;
        }
    }

    @action
    handleErrorFix() {
        if (!this.snackError) return;

        let errorMessage;
        if(this.snackError.type === "supabase") {
            errorMessage = `I encountered an error while running the SQL query: "${this.snackError.message}"

Please analyze this error carefully and help me fix it. Consider:
1. The specific error message and its root cause
2. Database schema, triggers, functions, RLS policies
3. Any related code that might be affected
4. Dependencies that might be missing or misconfigured

Please provide a detailed solution.`
        } else {
            errorMessage = `I encountered an error in Expo: "${this.snackError.message}"

Please analyze this error carefully and help me fix it. Consider:
1. The specific error message and its root cause
2. Any related code that might be affected
3. Dependencies that might be missing or misconfigured
4. Similar patterns in the codebase that work correctly
5. Minified react errors need to traced using where the issue is happening

Please provide a detailed solution.`;
        }


        this.currentMessage = errorMessage;
        this.markErrorAsHandled();
    }

    @action
    async sendAutoFixError(error: any) {
        console.log('error', error)
        const message = error?.prettyStack || error?.stack || error?.message || error
        // const autoFixErrors = [
        //     'is not defined',
        //     'Unexpected token, expected "," ',
        //     'Cannot read properties of undefined'
        // ]
        //
        // if(autoFixErrors.some(errorMessage => message.includes(errorMessage))) {
        //   // Attempt to auto fix
        //
        //
        //
        //   // Mark the error as handled
        //   this.markErrorAsHandled();
        //   return; // Skip sending the error message to the chat
        // }

        const errorMessage = `I encountered an error: 
${message}

Platform: ${error?.plaform || 'web'}

%%Error code: ${generateUUID()}%%

Please analyze this error carefully and help me fix it. Consider:
1. The specific error message and its root cause
2. Any related code that might be affected
3. Dependencies that might be missing or misconfigured
4. Similar patterns in the codebase that work correctly
5. Minified react errors need to traced using where the issue is happening
6. Examining means examining, don't write code to examine. Try to understand why things might be breaking and apply the most probable fix.
`;

        this.currentMessage = errorMessage;
        this.markErrorAsHandled();
    }

    @action
    addFileOperation(operation: FileOperation) {
        this.fileOperations.set(operation.path, operation);
    }

    @action
    async deleteMessage(messageId: string, isGroupedMessage: boolean = false) {
        try {
            // Add isGroupedMessage as a query parameter
            const url = isGroupedMessage 
                ? `/api/chat/${this.id}/message/${messageId}?isGroupedMessage=true` 
                : `/api/chat/${this.id}/message/${messageId}`;
            
            const result = await fetch(url, {method: "DELETE"})
            const response = await result.json();
            if(!response.success) {
                throw new Error('Error restoring checkpoint. Please contact support.');
            }
            await this.loadFiles();
            return response.latestMessageId;
        } catch (error) {
            throw new Error('Error restoring checkpoint. Please contact support.');
        }
    }

    @action
    async removeLatestFileState() {
        try {
            const result = await fetch(`/api/chat/${this.id}/files`, {method: "DELETE"})
            await result.json();
            await this.loadFiles();
            return "ok";
        } catch (error) {
            return null;
        }
    }

    @action
    markStreamingComplete() {
        this.hasStartedStreaming = false;
        // Detect if the user is in the background. If yes, store the update and on visibility change, update the snack.
    }

    @action
    markAsFailed() {
        if(this.hasStartedStreaming) {
            // There are code changes available. Let's try to continue from where we left off.
            this.hasStartedStreaming = false;
        }
    }

    @action
    setChangesWhileInactive(value: boolean) {
        this.changesWhileInactive = value;
    }

    @action
    async updateFileTree(codeBlock: CodeBlock) {
        if (codeBlock && codeBlock.type) {
            this.hasStartedStreaming = true;
            const filePath = codeBlock.absolutePath || codeBlock.fileName;
            if (!filePath) return
            const updatedTree = [...this.fileTree];

            // If we have a path with directories, create them

            // Single file at root level
            const existingFileIndex = updatedTree.findIndex(item =>
                item.name === filePath
            );

            if (existingFileIndex !== -1) {
                updatedTree[existingFileIndex] = {
                    ...updatedTree[existingFileIndex],
                    // @ts-ignore
                    content: codeBlock.content as unknown as string,
                    changes: ((updatedTree[existingFileIndex] as FileNode).changes || 0) + 1
                };
            } else {
                updatedTree.push({
                    type: 'file',
                    name: filePath,
                    content: codeBlock.content,
                    changes: 0,
                    language: 'typescript'
                });
            }

            this.setFileTree(updatedTree);
            this.updateActiveFile();
        }
    }

    @action
    updateActiveFile() {
        const currentFile = this.fileTree.find(item =>
             item.name === this.activeFile?.name
        );

        if (currentFile && currentFile.name) {
            this.setActiveFile({
                name: currentFile.name,
                content: currentFile.content,
                language: currentFile.language || 'typescript',
            } as FileNode);
        }
    }

    @action
    async loadFiles(options?: {anonymousId?: string}) {
        const {anonymousId} = options || {};
        try {
            return fetch(`/api/project/${this.projectId}/files`, {
                headers: {
                    'x-anonymous-id': anonymousId || '',
                }
            })
                .then(action((response) => {
                    if (response.ok) {
                        return response.json();
                    }
                }))
                .then(action((data) => {
                    if (data) {
                        this.setFileTree(data.files, data.dependencies)
                        // this.setDependencies(data.dependencies);
                        this.pushUpdatesToPreview()
                    }
                    return "ok"
                }))
        } catch (error) {
            console.error('Error loading file state:', error);
        }
    }

    @computed
    get activeOperations() {
        return Array.from(this.fileOperations.values()).filter(
            op => op.status === 'pending'
        );
    }

    @computed
    get isProcessing() {
        return this.state.status === 'loading' || this.state.status === 'streaming';
    }

    @computed
    get v2FileTree() {
        return this.fileTree
    }
    
    @action
    async fetchChatData() {
        try {
            const response = await fetch(`/api/chat/${this.id}`);
            if (response.ok) {
                const chatData = await response.json();
                if (chatData?.projectId) {
                    this.projectId = chatData.projectId;
                    return chatData;
                }
            }
            return null;
        } catch (error) {
            console.error('Failed to fetch chat data:', error);
            return null;
        }
    }
    
    @action
    async fetchProjectChats() {
        if (!this.projectId) {
            await this.fetchChatData();
        }
        
        if (this.projectId) {
            try {
                const response = await fetch(`/api/project/${this.projectId}/chat`);
                if (response.ok) {
                    return await response.json();
                }
            } catch (error) {
                console.error('Failed to fetch project chats:', error);
            }
        }
        return [];
    }


    /**
     * Loads a historical version of a project
     * @param projectId Project ID
     * @param chatId Chat ID
     * @param messageId Message ID of the historical version
     * @param activeMessageId Currently active message ID
     * @returns Promise resolving to true if successful, false otherwise
     */
    // @action
    loadHistoricalVersion = async (projectId: string, chatId: string, messageId: string, activeMessageId: string): Promise<boolean> => {
    return false;
        //     try {
        //     this.isLoadingHistoricalVersion = true;
        //     this.isViewingHistoricalVersion = true;
        //     this.historicalMessageId = activeMessageId;
        //
        //     // Fetch historical file state from API
        //     const response = await fetch(`/api/project/${projectId}/chat/${chatId}/message/${messageId}`);
        //
        //     if (!response.ok) {
        //         const errorText = await response.text();
        //         console.error('Failed to load historical version:', errorText);
        //
        //         this.historicalMessageId = null;
        //
        //         this.isLoadingHistoricalVersion = false;
        //         this.isViewingHistoricalVersion = false;
        //         this.historicalMessageId = null;
        //
        //         return false;
        //     }
        //
        //     const fileState = await response.json();
        //
        //     // Update the Snack with historical files
        //     const instance = this.rootStore.snackStore.snackInstances.get(chatId);
        //     if (instance) {
        //         instance.snack.updateFiles(this.rootStore.snackStore.convertToSnackFiles(fileState.files));
        //
        //         // Detect and update dependencies for historical version
        //         // await this.rootStore.snackStore.updateDependenciesFromFiles(instance.snack, fileState.files, chatId);
        //
        //         instance.snack.sendCodeChanges();
        //
        //         // Update UI state
        //         this.rootStore.snackStore.updateSnackState(chatId, {
        //             isLoading: false,
        //             error: null,
        //             historicalMessageId: activeMessageId
        //         });
        //
        //         this.isViewingHistoricalVersion = true;
        //         this.historicalMessageId = activeMessageId;
        //         this.isLoadingHistoricalVersion = false;
        //
        //         return true;
        //     }
        //
        //     this.isLoadingHistoricalVersion = false;
        //     return false;
        // } catch (error) {
        //     console.error('Error loading historical version:', error);
        //
        //         this.rootStore.snackStore.updateSnackState(chatId, {
        //         isLoading: false,
        //         error: `Error loading historical version: ${error}`,
        //         isHistoricalVersion: false,
        //         historicalMessageId: null
        //     });
        //
        //     this.isLoadingHistoricalVersion = false;
        //     this.isViewingHistoricalVersion = false;
        //     this.historicalMessageId = null;
        //
        //     return false;
        // }
    }

    @action
    async performAnalysis(message: any, messages: UIMessage[]) {

                this.postStreamAnalysisStatus = 'idle';
                if (typeof message.content === 'string' &&
                    message.content.length > 200 &&
                    (message.content.includes('<MO_FILE') || message.content.includes('<MO_DIFF') || message.content.includes('<MO_DATABASE_QUERY'))) {
                    try {
                        runInAction(() => {
                            this.postStreamAnalysisStatus = 'loading';
                        })
                        // Call the post-stream analysis API
                        const response = await fetch('/api/post-stream-analysis', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                chatId: this.id,
                                messageId: message.id,
                                files: this.fileTree,
                                messages
                            })
                        });

                        if (response.ok) {
                            const result = await response.json();
                            runInAction(() => {
                                this.postStreamAnalysis = {
                                    analysis: result.analysis,
                                    message: result.userFeedbackMessage
                                }
                                this.postStreamAnalysisStatus = 'loaded';
                            })

                            // Log analysis details for debugging
                            console.log('Code analysis completed:', {
                                recommended_action: result.analysis.recommendedAction,
                                has_issues: result.analysis.potentialIssues.length > 0
                            });
                        } else {
                            console.error('Failed to analyze code changes:', await response.text());
                            runInAction(() => {
                                this.postStreamAnalysisStatus = 'errored';
                            })
                        }
                    } catch (error) {
                        console.error('Error during post-stream analysis:', error);
                        runInAction(() => {
                            this.postStreamAnalysisStatus = 'errored';
                        })
                        Sentry.captureException(error);
                    }
                }
    }
    //
    // @action
    /**
     * Save secrets to Supabase project
     * @param secrets Record of secret names and values
     * @param projectId The project ID
     * @returns Promise that resolves when secrets are saved
     */
    @action
    saveSecrets = async (secrets: Record<string, string>, projectId: string, append: (message: Message | CreateMessage, chatRequestOptions?: ChatRequestOptions) => Promise<string | null | undefined>): Promise<boolean> => {
        try {
            // For each secret, make an API call to save it
            const secretEntries = Object.entries(secrets);

            await fetch(`/api/project/${projectId}/supabase/secrets`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    secrets: secretEntries.map(secret => ({name: secret[0], value: secret[1]})),
                    scope: 'project', // Default scope
                    environment: 'all'  // Default environment
                })
            });

            const secretsNames = Object.keys(secrets);
            // Notify the user that secrets were saved
            append({
                content: `I have added the ${secretsNames.join(',')} secret(s). Please continue.`,
                role: 'user'
            });
            return true;
        } catch (error) {
            console.error('Error saving secrets:', error);
            toast.error(`Failed to save secrets: ${error instanceof Error ? error.message : 'Unknown error'}`)
            return false;
        }
    };
    
    restoreLatestVersion = async (projectId: string, chatId: string): Promise<boolean> => {
   return  false
    //     try {
    //         // Update UI to show loading state
    //         this.updateSnackState(chatId, {
    //             isLoading: true,
    //             error: null
    //         });
    //
    //         // Fetch latest file state from API
    //         const response = await fetch(`/api/project/${projectId}/files`);
    //
    //         if (!response.ok) {
    //             const errorText = await response.text();
    //             console.error('Failed to restore latest version:', errorText);
    //
    //             this.updateSnackState(chatId, {
    //                 isLoading: false,
    //                 error: `Failed to restore latest version: ${errorText}`
    //             });
    //
    //             return false;
    //         }
    //
    //         const fileState = await response.json();
    //
    //         // Update the Snack with latest files
    //         const instance = this.snackInstances.get(chatId);
    //         if (instance) {
    //             instance.snack.updateFiles(this.convertToSnackFiles(fileState.files));
    //
    //             // Detect and update dependencies for historical version
    //             await this.updateDependenciesFromFiles(instance.snack, fileState.files, chatId);
    //
    //             instance.snack.sendCodeChanges();
    //
    //             // Update UI state
    //             this.updateSnackState(chatId, {
    //                 isLoading: false,
    //                 error: null,
    //                 isHistoricalVersion: false,
    //                 historicalMessageId: null
    //             });
    //
    //             this.isViewingHistoricalVersion = false;
    //             this.historicalMessageId = null;
    //
    //             return true;
    //         }
    //
    //         return false;
    //     } catch (error) {
    //         console.error('Error restoring latest version:', error);
    //
    //         this.updateSnackState(chatId, {
    //             isLoading: false,
    //             error: `Error restoring latest version: ${error}`
    //         });
    //
    //         return false;
    //     }
    };

}
