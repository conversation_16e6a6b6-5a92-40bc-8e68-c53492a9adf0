import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {CheckCircle, CheckCircle2, DownloadIcon, InfoIcon, LoaderIcon, PlusIcon, Rocket} from 'lucide-react';
import { ExpoGoAlert } from './ExpoGoAlert';
import Link from 'next/link';
import PublishButton from './PublishButton';
import { FeatureGate } from '@/components/subscription/feature-gate';
import { useRouter, usePathname } from 'next/navigation';
import Image from "next/image";
import { ShareDialog } from './ShareDialog';
import { observer } from 'mobx-react-lite';
import {useStores} from "@/stores/utils/useStores";
import { toast } from 'sonner';
import { IntegrationDialog } from './IntegrationDialog';
import { ProjectSettingsDialog } from './ProjectSettingsDialog';
import { cn } from '@/lib/utils';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Project } from '@/lib/db/schema';
import { SidebarTrigger } from '@/components/ui/sidebar';
import {ProjectSession} from "@/stores/ProjectSessionStore";
import {LinkButton} from "@/components/ui/link-button";
import { trackFeatureEvent, trackIdeaFlowEvent } from '@/lib/analytics/track';
import {Alert, AlertDescription} from "@/components/ui/alert";

interface HeaderProps {
    chatId?: string;
    isInitial?: boolean;
    visibility?: 'public' | 'private';
    activeTab?: 'chat' | 'deployments' | 'settings';
    projectId?: string;
    chatTitle?: string;
    showActions: boolean;
}

export const Header: React.FC<HeaderProps> = observer(({
    chatId,
    isInitial,
    visibility = 'private',
    activeTab = 'chat',
    projectId,
    chatTitle,
    showActions
}) => {
    const router = useRouter();
    const pathname = usePathname();
    const {generatorStore, integrationStore} = useStores();
    let session: ProjectSession | undefined;
    if(chatId) {
        session = generatorStore.getActiveSession(chatId);
    }
    const [downloading, setDownloading] = React.useState(false);
    const [activeProvider, setActiveProvider] = React.useState<string | null>(null);
    const [projectData, setProjectData] = useState<Project | null>(null);
    const [isSettingsDialogOpen, setIsSettingsDialogOpen] = useState(false);
    
    // Base URL for reference

    // Fetch project data when component mounts or projectId changes
    useEffect(() => {
        const fetchData = async () => {
            console.log('Fetching Data...', projectId);
            if (projectId) {
                const data = generatorStore.getProjectData(projectId);
                setProjectData(data);
            }
        };
        
        fetchData();
    }, [projectId, generatorStore]);


    const onClick = () => {
        if(session) {
            // Track when user opens the preview dialog from mobile view
            trackFeatureEvent('PREVIEW_REFRESHED', {
                feature_name: 'mobile_preview_dialog',
                is_enabled: true,
                user_type: 'free', // This should be dynamically determined
                trigger_source: 'header_button'
            });
            
            session.setPreviewDialogOpen(true);
        }
    }

    const download = () => {
        // Track download initiation
        trackFeatureEvent('DEPLOYMENT_INITIATED', {
            feature_name: 'project_download',
            is_enabled: true,
            user_type: 'free', // This should be dynamically determined
            platform_type: 'local',
            project_id: projectId
        });
        
        setDownloading(true);
        const startTime = Date.now();
        
        fetch(`/api/project/${projectId}/download`, {method: "POST"})
            .then(async response => {
                setDownloading(false);
                if (response.ok) {
                    const blob = await response.blob();
                    // Create download link
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `project-${chatId}.zip`;
                    a.click();
                    window.URL.revokeObjectURL(url);
                    toast.success('Downloaded successfully');
                    
                    // Track successful download
                    trackFeatureEvent('DEPLOYMENT_COMPLETED', {
                        feature_name: 'project_download_success',
                        is_enabled: true,
                        user_type: 'free', // This should be dynamically determined
                        platform_type: 'local',
                        project_id: projectId,
                        deployment_time: Date.now() - startTime
                    });
                } else {
                    console.log('Response', response)
                    toast.error('Download failed');
                    
                    // Track failed download
                    trackFeatureEvent('DEPLOYMENT_FAILED', {
                        feature_name: 'project_download_failed',
                        is_enabled: true,
                        user_type: 'free', // This should be dynamically determined
                        platform_type: 'local',
                        project_id: projectId,
                        deployment_time: Date.now() - startTime,
                        trigger_source: 'api_error'
                    });
                }
            })
            .catch(err => {
                setDownloading(false);
                console.log(err)
                toast.error('Download failed');
                
                // Track error in download
                trackFeatureEvent('DEPLOYMENT_FAILED', {
                    feature_name: 'project_download_error',
                    is_enabled: true,
                    user_type: 'free', // This should be dynamically determined
                    platform_type: 'local',
                    project_id: projectId,
                    deployment_time: Date.now() - startTime,
                    trigger_source: 'network_error'
                });
            });
    }

    useEffect(() => {
        console.log('integrationStore.providers', JSON.stringify(integrationStore.providers))
    }, [integrationStore.providers])

    return (
            <div className={`${showActions? 'h-24': 'h-14'} md:h-14 border-b border-[#181A1F] bg-background/70`}>
                <div className="flex shrink-0 px-4 py-2 justify-between items-center">
                {/* Left section */}
                <div className="flex items-center gap-2">
                    <SidebarTrigger />
                    {projectData && (
                        <Breadcrumb className="hidden md:flex">
                            <BreadcrumbList>
                                <BreadcrumbItem>
                                    <button 
                                        onClick={() => setIsSettingsDialogOpen(true)}
                                        className="text-muted-foreground hover:text-primary max-w-48 truncate text-xs flex items-center gap-1 cursor-pointer"
                                    >
                                        {projectData.appName || 'Untitled Project'}
                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-chevron-down">
                                            <path d="m6 9 6 6 6-6"/>
                                        </svg>
                                    </button>
                                </BreadcrumbItem>
                                <BreadcrumbSeparator />
                                {
                                    chatId ?
                                        <div className="flex">
                                            <BreadcrumbItem>
                                                <BreadcrumbLink className="text-accent font-medium max-w-96 truncate text-xs">
                                                    <span className="text-xs italic text-primary-foreground"></span> {chatTitle || 'Untitled Chat'}
                                                </BreadcrumbLink>
                                            </BreadcrumbItem>
                                            <LinkButton 
                                                        href={`/projects/${projectData.id}/chats/new`}
                                                        variant="default" size="sm"
                                                        className="ml-4"
                                                        onClick={() => {
                                                            // Track when user starts a new chat
                                                            trackIdeaFlowEvent('PROJECT_CREATED', {
                                                                project_id: projectData.id,
                                                                chat_id: chatId
                                                            });
                                                        }}
                                                        >
                                                <PlusIcon className='h-2 w-2'/>
                                                New Chat
                                            </LinkButton>
                                        </div> :
                                        null
                                }
                            </BreadcrumbList>
                        </Breadcrumb>
                    )}
                </div>

                {
                    session ?
                        <>
                            {/* Right section */}
                            <div className="flex items-center gap-2">
                                {/*<div className="hidden md:flex">*/}
                                {/*    <SubscriptionStatus/>*/}
                                {/*</div>*/}

                                {
                                    showActions ?
                                        <>
                                            <FeatureGate
                                                featureId="code_download"
                                                upgradeMessage="Upgrade to download code"
                                                wrapHandler={true}
                                            >
                                                {({handleClick}) => (
                                                    <>
                                                        {integrationStore.providers.filter(p => p.id === "supabase").map((provider) => (
                                                            <Button
                                                                key={provider.id}
                                                                variant="default"
                                                                size="sm"
                                                                className={`bg-gray-900 hover:bg-gray-900/90 text-primary-foreground relative hidden md:flex`}
                                                                onClick={handleClick(() => {
                                                                    // Track when user initiates Supabase connection
                                                                    trackFeatureEvent('SUPABASE_CONNECTION_INITIATED', {
                                                                        feature_name: 'supabase_connect',
                                                                        is_enabled: true,
                                                                        user_type: 'free', // This should be dynamically determined
                                                                        platform_type: 'supabase',
                                                                        project_id: projectId
                                                                    });
                                                                    setActiveProvider(provider.id);
                                                                })}
                                                            >
                                                                <Image
                                                                    src={provider.logo}
                                                                    alt={provider.name}
                                                                    width={14}
                                                                    height={14}
                                                                    className="dark:filter"
                                                                />
                                                                <span className="flex items-center gap-1">{projectData?.connectionId ? 'Supabase connected' : 'Connect Supabase'}</span>
                                                                {
                                                                    projectData?.connectionId ?
                                                                        <CheckCircle2 size={4}/> :
                                                                        null
                                                                }
                                                            </Button>
                                                        ))}
                                                    </>
                                                )}
                                            </FeatureGate>

                                            {activeProvider && (
                                                <IntegrationDialog
                                                    providerId={activeProvider}
                                                    open={!!activeProvider}
                                                    chatId={chatId}
                                                    projectId={projectId}
                                                    onOpenChange={(open) => !open && setActiveProvider(null)}
                                                />
                                            )}
                                            {(chatId) && (
                                                <FeatureGate
                                                    featureId="code_download"
                                                    upgradeMessage="Upgrade to download code"
                                                    wrapHandler={true}
                                                >
                                                    {({ handleClick }) => (
                                                        <Button
                                                            className="button hidden md:flex"
                                                            variant="ghost"
                                                            disabled={downloading}
                                                            onClick={handleClick(download)}
                                                            title="Download code"
                                                        >
                                                            {downloading ? (
                                                                <div className="animate-spin absolute text-zinc-500">
                                                                    <LoaderIcon/>
                                                                </div>
                                                            ) : (
                                                                <DownloadIcon className="h-4 w-4"/>
                                                            )}
                                                        </Button>
                                                    )}
                                                </FeatureGate>
                                            )}

                                            {(projectId) && (
                                                <div className="">
                                                    <FeatureGate
                                                        featureId="code_download"
                                                        upgradeMessage="Upgrade to download code"
                                                        wrapHandler={true}
                                                    >
                                                        {({ handleClick }) => (
                                                            <PublishButton projectId={projectId} handleClick={handleClick} />
                                                        )}
                                                    </FeatureGate>
                                                </div>
                                            )}
                                        </>:
                                        null

                                }

                                {/*{(chatId) && (*/}
                                {/*    <ShareDialog*/}
                                {/*        chatId={chatId}*/}
                                {/*        initialVisibility={visibility}*/}
                                {/*    />*/}
                                {/*)}*/}

                                <LinkButton
                                    href="https://discord.gg/Cpda56yVYY"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    onClick={() => {
                                        // Track when user clicks on Discord help link
                                        trackFeatureEvent('DISCORD_HELP_TRIGGERRED', {
                                            feature_name: 'discord_help',
                                            is_enabled: true,
                                            user_type: 'free', // This should be dynamically determined
                                            trigger_source: 'header_button',
                                            project_id: projectId
                                        });
                                    }}
                                    variant="link"
                                    size="sm"
                                    className="flex items-center gap-2"
                                >

                                        <Image src={"/discord.jpg"} alt={"discord logo"} width={28} height={28} className="h-5 w-5 rounded-full"/>
                                </LinkButton>
                                {session.previewUrl && <ExpoGoAlert playerUrl={session.previewUrl} />}
                            </div>
                        </> :
                        null
                }

            </div>

            {
                showActions ?
                    <div className="px-1 flex md:hidden justify-between items-center">
                        <div className="w-1/2 px-3">
                            <div className="flex justify-start items-center">
                                <InfoIcon className="w-3 h-3 text-accent"/>
                                <p className="text-[10px] text-accent leading-tight pl-1 font-bold">10+ more features on desktop</p>
                            </div>
                        </div>
                        <div className="w-1/2 flex justify-end">
                            <Button variant="secondary" onClick={onClick}>
                                Click to open preview
                            </Button>
                        </div>
                        <div></div>
                    </div> :
                    null
            }

                {projectId && projectData && (
                    <ProjectSettingsDialog
                        isOpen={isSettingsDialogOpen}
                        onClose={() => setIsSettingsDialogOpen(false)}
                        projectId={projectId}
                        chatId={chatId}
                    />
                )}

        </div>
    );
});
