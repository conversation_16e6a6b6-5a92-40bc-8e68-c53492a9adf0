'use client';

import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { useStores } from '@/stores/utils/useStores';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { SupabaseTableUI } from './SupabaseTableUI';
import { Button } from '@/components/ui/button';
import { Database, FunctionSquare, Table } from 'lucide-react';
import { FunctionsList } from '@/components/integrations/supabase/FunctionsList';
import { ConnectionStatus } from '@/components/integrations/supabase/ConnectionStatus';
import {IntegrationDialog} from "@/components/generator/IntegrationDialog";

interface SupabasePanelProps {
  projectId: string;
  chatId: string;
}

export const SupabasePanel = observer(({ projectId, chatId }: SupabasePanelProps) => {
  const { generatorStore, supabaseStore } = useStores();
  const session = generatorStore.getActiveSession(chatId);
  const [activeTab, setActiveTab] = useState('functions');
  const [integrationOpen, setIntegrationOpen] = useState(false);
  
  // Get project from the session's projectId
  const project = session ? generatorStore.getProjectData(projectId) : null;

  // Check if project is connected to Supabase
  const isConnected = !!project?.supabaseProjectId && !!project?.connectionId;

  // Mark the tab as opened when the component mounts
  // This will trigger data loading only the first time the tab is opened
  useEffect(() => {
    if (!supabaseStore.tabOpened) {
      supabaseStore.setTabOpened();
      
      // Load functions data if this is the first time opening the tab
      if (isConnected && project && !supabaseStore.hasLoadedProject(projectId)) {
        supabaseStore.fetchFunctions(project);
      }
    }
  }, [projectId, supabaseStore, generatorStore, isConnected, project]);

  if (!project) {
    return (
      <Card className="w-full h-full flex items-center justify-center">
        <CardContent>
          <p className="text-muted-foreground">Project not found</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full h-full flex flex-col overflow-hidden">
      <ConnectionStatus project={project} />
      
      {isConnected ? (
        <Tabs 
          defaultValue={activeTab} 
          onValueChange={setActiveTab}
          className="flex-1 flex flex-col overflow-hidden"
        >
          <div className="border-b px-4">
            <TabsList className="h-10 bg-transparent">
              <TabsTrigger 
                value="functions" 
                className="data-[state=active]:bg-muted flex items-center gap-2"
              >
                <FunctionSquare className="h-4 w-4" />
                <span>Functions</span>
              </TabsTrigger>
              <TabsTrigger 
                value="tables" 
                className="data-[state=active]:bg-muted flex items-center gap-2"
              >
                <Table className="h-4 w-4" />
                <span>Tables</span>
              </TabsTrigger>
              <TabsTrigger 
                value="storage" 
                className="data-[state=active]:bg-muted flex items-center gap-2"
              >
                <Database className="h-4 w-4" />
                <span>Storage</span>
              </TabsTrigger>
            </TabsList>
          </div>
          
          <div className="flex-1 overflow-auto p-4">
            <TabsContent value="functions" className="h-full m-0">
              <FunctionsList projectId={projectId} />
            </TabsContent>
            
            <TabsContent value="tables" className="h-full m-0">
              <SupabaseTableUI />
            </TabsContent>
            
            <TabsContent value="storage" className="h-full m-0">
              <Card className="w-full h-full flex items-center justify-center">
                <CardContent>
                  <p className="text-muted-foreground">Storage view coming soon</p>
                </CardContent>
              </Card>
            </TabsContent>
          </div>
        </Tabs>
      ) : (
        <div className="flex-1 flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Connect Supabase</CardTitle>
              <CardDescription>
                Connect your Supabase project to manage functions, tables, and storage.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                className="w-full" 
                onClick={() => setIntegrationOpen(true)}
              >
                Connect Supabase to app
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      <IntegrationDialog
          providerId={'supabase'}
          open={integrationOpen}
          chatId={chatId}
          projectId={projectId}
          onOpenChange={() => setIntegrationOpen(false)}
      />
    </div>
  );
});
