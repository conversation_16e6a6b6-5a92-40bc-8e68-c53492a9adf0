'use client';

import React, { memo, useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { FileText, ChevronDown, ChevronRight, Folder } from 'lucide-react';

interface GetFileContentsToolResultProps {
  path: string;
  reason: string;
  isDirectory?: boolean;
  className?: string;
}

export const GetFileContentsToolResult = memo(function GetFileContentsToolResult({
  path,
  reason,
  isDirectory = false,
  className
}: GetFileContentsToolResultProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);

  const displayPath = path.startsWith('/') ? path.slice(1) : path;

  return (
    <div
      className={cn(
        'mt-4 rounded-lg bg-card/80 p-2 shadow-sm relative overflow-hidden',
        'border border-border/50',
        'transition-all duration-300 ease-in-out',
        !isExpanded && 'py-1.5',
        'border-green-600/30 bg-green-600/5',
        className
      )}
    >
      <div>
        <div
          className="flex items-center justify-between text-xs font-medium cursor-pointer hover:bg-muted/50 p-1 rounded-md transition-colors"
          onClick={toggleExpanded}
        >
          <div className="flex items-center gap-1.5">
            {isExpanded ? (
              <ChevronDown className="h-3 w-3 text-muted-foreground" />
            ) : (
              <ChevronRight className="h-3 w-3 text-muted-foreground" />
            )}

            {isDirectory ? (
              <Folder className="h-3 w-3 text-green-600" />
            ) : (
              <FileText className="h-3 w-3 text-green-600" />
            )}

            <span className="text-foreground">
              {isDirectory ? `Directory listing: ${displayPath}` : `File content: ${displayPath}`}
            </span>
          </div>

          <Badge variant="outline" className="text-[10px] h-4">
            {isDirectory ? 'Directory' : 'File'}
          </Badge>
        </div>

        <div className={cn(
          "space-y-0.5 overflow-hidden transition-all duration-300 ease-in-out",
          isExpanded ? "max-h-[500px] opacity-100 mt-0.5" : "max-h-0 opacity-0 mt-0"
        )}>
          <div className="px-2 py-1 text-xs">
            <div className="font-medium mb-1">Reason for request:</div>
            <div className="text-muted-foreground bg-muted/30 p-1.5 rounded-md font-mono text-[11px]">
              {reason}
            </div>

            <div className="mt-2">
              <div className="font-medium mb-1">Path:</div>
              <div className="text-muted-foreground bg-muted/30 p-1.5 rounded-md font-mono text-[11px]">
                {displayPath}
              </div>
            </div>

            <div className="mt-2">
              <div className="font-medium mb-1">Type:</div>
              <div className="text-muted-foreground bg-muted/30 p-1.5 rounded-md font-mono text-[11px]">
                {isDirectory ? 'Directory listing' : 'File content retrieval'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});
