'use client';

import type {
    Attachment,
    ChatRequestOptions,
    CreateMessage,
    Message,
} from 'ai';
import cx from 'classnames';
import type React from 'react';
import {useRef,
    useEffect,
    useState,
    useCallback,
    useMemo,
    type Dispatch,
    type SetStateAction,
    type ChangeEvent,
    memo,
} from 'react';
// Removed direct store import
import {toast} from 'sonner';
import {useLocalStorage, useWindowSize} from 'usehooks-ts';

import {sanitizeUIMessages} from '@/lib/utils';

import {PreviewAttachment} from './preview-attachment';
import equal from 'fast-deep-equal';
import {Textarea} from '../ui/textarea';
import {Button} from "@/components/ui/button";
import { trackMessageEvent } from "@/lib/analytics/track";
import {
    ArrowUpIcon,
    PaperclipIcon,
    PauseIcon,
    ImageIcon,
    PlusIcon,
    DatabaseIcon,
    BrainIcon,
    ChevronDownIcon,
    Loader2Icon,
    XIcon,
    CheckCircleIcon,
    Settings, Box,
    Fullscreen, BoxSelectIcon,
    MessageSquareIcon
} from 'lucide-react';
import {SuggestedActions} from "@/components/base/suggested-actions";
import {useIsMobile} from "@/hooks/use-mobile";
import {SubscriptionStatus} from "@/components/subscription-status";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
    DropdownMenuGroup,
    DropdownMenuLabel
} from "@/components/ui/dropdown-menu";
import {SoundSettings, useSoundSettings} from "@/components/settings/sound-settings";
import Image from "next/image";
import {LinkButton} from "@/components/ui/link-button";
import {AgentModeSettings, useAgentModeSettings} from "@/components/settings/agent-mode-settings";
import { ContextScreenshots } from "./context-screenshots";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { ContinueButton } from "./continue-button";
import {CONTINUE_PROMPT} from "@/lib/ai/prompts/streamlined-agent-prompt";
import { DiscussWithAI } from "@/components/discuss/DiscussWithAI";

function PureMultimodalInput({
                                 chatId,
                                 projectId,
                                 input,
                                 setInput,
                                 isLoading,
                                 stop,
                                 attachments,
                                 setAttachments,
                                 messages,
                                 setMessages,
                                 append,
                                 handleSubmit,
                                 className,
                                 droppedFiles,
                                 onVisualSelectionClicked,
                                 componentContexts,
                                 onRemoveComponentContext,
                                 onClearComponentContexts,
                                 selectMode,
                                 needsContinuation,
                                 inDesignMode
                             }: {
    chatId: string;
    projectId: string;
    input: string;
    setInput: (value: string) => void;
    isLoading: boolean;
    stop: () => void;
    attachments: Array<Attachment>;
    setAttachments: Dispatch<SetStateAction<Array<Attachment>>>;
    messages: Array<Message>;
    setMessages: Dispatch<SetStateAction<Array<Message>>>;
    append: (
        message: Message | CreateMessage,
        chatRequestOptions?: ChatRequestOptions,
    ) => Promise<string | null | undefined>;
    handleSubmit: (
        event?: {
            preventDefault?: () => void;
        },
        chatRequestOptions?: ChatRequestOptions,
    ) => void;
    className?: string;
    droppedFiles?: File[];
    onVisualSelectionClicked: () => void;
    componentContexts: Array<any>;
    onRemoveComponentContext: (id: string) => void;
    onClearComponentContexts: () => void;
    selectMode: boolean;
    needsContinuation: boolean;
    inDesignMode: boolean;
}) {
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const {width} = useWindowSize();
    const isMobile = useIsMobile();

    // State to track if the AI has completed writing since user was last active
    const [showCompletionNotice, setShowCompletionNotice] = useState(false);
    const [wasLoading, setWasLoading] = useState(false);
    
    // State to track if we should show the continue button
    const [showContinueButton, setShowContinueButton] = useState(needsContinuation);
    const [userActive, setUserActive] = useState(true);
    const [originalTitle, setOriginalTitle] = useState("");
    const [isDragging, setIsDragging] = useState(false);
    
    // Component contexts are now passed as props instead of using store directly

    // Audio reference for completion sound
    const completionSoundRef = useRef<HTMLAudioElement | null>(null);


    useEffect(() => {
        setShowContinueButton(needsContinuation && !isLoading);
    }, [needsContinuation, isLoading])
    // Get sound settings from localStorage
    const {playCompletionSound} = useSoundSettings();
    useEffect(() => {
        if (textareaRef.current) {
            adjustHeight();
        }
    }, []);

    // Initialize audio element
    useEffect(() => {
        completionSoundRef.current = new Audio('/sounds/ping.mp3');
        return () => {
            completionSoundRef.current = null;
        };
    }, []);

    // Track loading state changes to detect when AI completes writing
    useEffect(() => {
        // If was loading before and now it's not
        if (wasLoading && !isLoading) {
            // Play completion sound if enabled in settings
            if (playCompletionSound && completionSoundRef.current) {
                // Reset the audio to the beginning in case it was played before
                completionSoundRef.current.currentTime = 0;
                // Play the sound
                completionSoundRef.current.play().catch(err => {
                    // Handle any autoplay restrictions or other errors silently
                    console.log('Audio playback error:', err);
                });
            }

            // Show completion notice regardless of user activity
            setShowCompletionNotice(true);

            // Update page title with completion indicator if user is not active
            if (!userActive && originalTitle) {
                document.title = `✅ ${originalTitle}`;
            }
        }
        setWasLoading(isLoading);
    }, [isLoading, wasLoading, userActive, originalTitle, playCompletionSound]);

    // Store original page title on component mount
    useEffect(() => {
        setOriginalTitle(document.title);
    }, []);

    // Set up visibility change detection
    useEffect(() => {
        const handleVisibilityChange = () => {
            if (document.visibilityState === 'visible') {
                // Reset title when user returns to the page
                if (document.title.includes('✓')) {
                    document.title = originalTitle;
                }
                setUserActive(true);
            } else {
                setUserActive(false);
            }
        };

        // Set up focus/blur detection
        const handleFocus = () => {
            // Reset title when user focuses the window
            if (document.title.includes('✓')) {
                document.title = originalTitle;
            }
            setUserActive(true);
        };
        const handleBlur = () => setUserActive(false);

        document.addEventListener('visibilitychange', handleVisibilityChange);
        window.addEventListener('focus', handleFocus);
        window.addEventListener('blur', handleBlur);

        return () => {
            document.removeEventListener('visibilitychange', handleVisibilityChange);
            window.removeEventListener('focus', handleFocus);
            window.removeEventListener('blur', handleBlur);
        };
    }, [originalTitle]);

    // Auto-dismiss completion notice after 5 seconds
    useEffect(() => {
        if (showCompletionNotice) {
            const timer = setTimeout(() => {
                setShowCompletionNotice(false);
            }, 5000);
            return () => clearTimeout(timer);
        }
    }, [showCompletionNotice]);

    const adjustHeight = () => {
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = `${Math.max(120, textareaRef.current.scrollHeight) + 2}px`;
        }
    };

    const resetHeight = () => {
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = '120px';
        }
    };

    const [promptInLS, setPromptInLs] = useLocalStorage("pre_auth_redirect_prompt", '')

    const [localStorageInput, setLocalStorageInput] = useLocalStorage(
        `input_${chatId}`,
        '',
    );


    useEffect(() => {
        let timeout: NodeJS.Timeout;
        if (textareaRef.current) {
            const domValue = textareaRef.current.value;
            // Prefer DOM value over localStorage to handle hydration
            const finalValue = domValue || promptInLS || localStorageInput || '';
            setInput(finalValue);
            setPromptInLs('');
            timeout = setTimeout(() => {
                adjustHeight();
            }, 500);
        }
        // Only run once after hydration
        // eslint-disable-next-line react-hooks/exhaustive-deps
        return () => {
            if(timeout) {
                clearTimeout(timeout);
            }
        }
    }, []);

    useEffect(() => {
        setLocalStorageInput(input);
    }, [input, setLocalStorageInput, promptInLS, setLocalStorageInput]);


    const handleInput = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
        setInput(event.target.value);
        adjustHeight();
    };

    const fileInputRef = useRef<HTMLInputElement>(null);
    const [uploadQueue, setUploadQueue] = useState<Array<string>>([]);
    
    // Handle continue button click with default or custom prompt
    const handleContinue = useCallback(() => {
        append({
            content: CONTINUE_PROMPT,
            role: "user"
        })
    }, [append]);

    // Handle form submission
    const submitForm = useCallback(() => {
        if (input.length === 0 && attachments.length === 0 && componentContexts.length === 0) return;
        if (uploadQueue.length > 0) return; // Prevent submission while files are uploading

        // Check if component contexts are still uploading
        // Only check for uploading status if there are contexts with screenshots
        const contextsWithScreenshots = componentContexts.filter(context => 
            context.screenshot && context.screenshot.length > 0
        );
        
        if (contextsWithScreenshots.length > 0 && contextsWithScreenshots.some(context => context.isUploading)) {
            toast.info('Waiting for component screenshots to upload...');
            return;
        }
        
        // If we have component contexts, append them to the message content
        let messageToSend = input;
        
        // Prepare the attachments - both regular attachments and component screenshots
        const allAttachments = [...attachments];
        
        // Add component context screenshots to attachments (only if they have images)
        componentContexts.forEach(context => {
            if (context.imageUrl) {
                // Check if this attachment is already in the list
                const exists = allAttachments.some(att => att.url === context.imageUrl);
                if (!exists) {
                    allAttachments.push({
                        url: context.imageUrl,
                        name: `${context.componentName}.jpeg`,
                        contentType: 'image/jpeg'
                    });
                }
            }
            // Skip contexts without images - they're still sent in the message body
        });

        // Update the input with the new message that includes component contexts
        setInput(messageToSend);
        
        // Submit with all attachments
        handleSubmit(undefined, {
            body: {
                // Only send a few properties to ensure the data is clean in the database
                componentContexts: componentContexts.map(context => {
                    return {
                        componentName: context.componentName,
                        element: context.element,
                        sourceFile: context.sourceFile,
                        lineNumber: context.lineNumber,
                        imageUrl: context.imageUrl,
                    }
                }) || []
            },
            experimental_attachments: allAttachments,
        });
        
        setInput('');
        setAttachments([]);
        setPromptInLs('');
        setLocalStorageInput('');
        resetHeight();

        if (width && width > 768) {
            textareaRef.current?.focus();
        }

        // Clear component contexts after sending
        onClearComponentContexts();
        
        trackMessageEvent('SENT', {
            chat_id: chatId,
            project_id: projectId
        });
    }, [input, attachments, uploadQueue, handleSubmit, setInput, chatId, projectId, componentContexts, onClearComponentContexts]);

    const uploadFile = async (file: File) => {
        const formData = new FormData();
        formData.append('file', file);

        try {
            const response = await fetch('/api/files/upload', {
                method: 'POST',
                body: formData,
            });

            if (response.ok) {
                const data = await response.json();
                const {url, pathname, contentType} = data;

                // Return the attachment object instead of setting it directly
                // This avoids double-adding attachments
                return {
                    name: pathname,
                    url,
                    contentType,
                };
            } else {
                const {error} = await response.json();
                toast.error(error);
                return false;
            }
        } catch (error) {
            toast.error('Failed to upload file, please try again!');
            return false;
        }
    };

    // Process files (common function for both input change and drop)
    const processFiles = useCallback(
        async (files: File[]) => {
            if (!files.length) return;
            
            // Add files to upload queue with their names
            setUploadQueue(files.map((file) => file.name));
            
            try {
                // Process each file upload individually to show progress
                for (const file of files) {
                    const attachment = await uploadFile(file);
                    if (attachment) {
                        setAttachments((currentAttachments) => [
                            ...currentAttachments,
                            attachment,
                        ]);
                        
                        // Remove this file from the upload queue
                        setUploadQueue(current => current.filter(name => name !== file.name));
                    }
                }
            } catch (error) {
                console.error('Error uploading files!', error);
                toast.error('Failed to upload one or more files');
                setUploadQueue([]);
            }
        },
        [setAttachments],
    );
    
    // Handle file change from input element
    const handleFileChange = useCallback(
        (event: ChangeEvent<HTMLInputElement>) => {
            const files = Array.from(event.target.files || []);
            processFiles(files);
            // Reset the input value so the same file can be selected again
            if (fileInputRef.current) fileInputRef.current.value = '';
        },
        [processFiles],
    );

    // Function to filter out component context attachments from regular attachments
    // to avoid showing them twice
    const filteredAttachments = useMemo(() => {
        // Filter out attachments that are also in component contexts to avoid duplicates
        if (componentContexts.length > 0) {
            return attachments.filter(att => {
                // Check if this attachment is in component contexts
                // Only compare with contexts that have imageUrl
                return !componentContexts.some(context => {
                    return context.imageUrl && context.imageUrl === att.url;
                });
            });
        }
        return attachments;
    }, [attachments, componentContexts]);

    useEffect(() => {
        if(droppedFiles && droppedFiles.length) {
            processFiles(droppedFiles)
        }
    }, [droppedFiles])
    
    // Drag and drop handlers
    const handleDragEnter = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    }, []);
    
    const handleDragLeave = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    }, []);
    
    const handleDragOver = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (!isDragging) setIsDragging(true);
    }, [isDragging]);
    
    const handleDrop = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
        
        const files = Array.from(e.dataTransfer.files);
        processFiles(files);
    }, [processFiles]);

    return (
        <div 
            ref={containerRef}
            className="relative w-full flex flex-col gap-4"
            onDragEnter={handleDragEnter}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
        >

            <div className="absolute top-[-40px] left-0 right-0">
                <SubscriptionStatus/>
            </div>
            
            {/* Continue button appears at the top of the input area */}
            {showContinueButton && (
                <ContinueButton
                    onContinue={handleContinue}
                    isLoading={isLoading}
                    shouldShow={true}
                    className="mb-3" /* Add margin to create space between button and input */
                />
            )}
            
            {/* Component Context Screenshots */}
            {componentContexts.length > 0 && (
                <ContextScreenshots 
                    screenshots={componentContexts}
                    onRemove={(id) => {
                        console.log('Removing component context with ID:', id);
                        onRemoveComponentContext(id);
                    }}
                    onClear={() => {
                        console.log('Clearing all component contexts');
                        onClearComponentContexts();
                    }}
                />
            )}
            
            <input
                type="file"
                className="fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none"
                ref={fileInputRef}
                multiple
                onChange={handleFileChange}
                tabIndex={-1}
            />

            {/* Regular file attachments */}
            {(filteredAttachments.length > 0 || uploadQueue.length > 0) && (
                <div className="flex-1 flex">
                    {filteredAttachments.map((attachment: Attachment, index: number) => (
                        <PreviewAttachment 
                            key={attachment.url} 
                            attachment={attachment}
                            onRemove={() => {
                                setAttachments(prev => prev.filter((_, i) => i !== index));
                            }}
                        />
                    ))}

                    {uploadQueue.map((filename) => (
                        <PreviewAttachment
                            key={filename}
                            attachment={{
                                url: '',
                                name: filename,
                                contentType: '',
                            }}
                            isUploading={true}
                        />
                    ))}
                </div>
            )}

            <div className="relative">
                {/* Drag overlay with instructions */}
                {isDragging && (
                    <div className="absolute inset-0 flex items-center justify-center z-50 pointer-events-none rounded-2xl overflow-hidden">
                        <div className="absolute inset-0 bg-primary/10 border-2 border-dashed border-primary/30"></div>
                        <div className="bg-background/80 backdrop-blur-sm p-4 rounded-lg shadow-lg text-center z-10">
                            <PaperclipIcon className="h-8 w-8 mx-auto mb-2 text-primary" />
                            <p className="text-sm font-medium">Drop files here</p>
                            <p className="text-xs text-muted-foreground">Images, documents, and other files</p>
                        </div>
                    </div>
                )}
                
                {/* Subtle shimmer effect when AI is writing */}
                {isLoading && (
                    <div className="absolute inset-x-0 top-0 h-1 overflow-hidden z-10">
                        <div
                            className="h-full w-1/3 bg-gradient-to-r from-transparent via-primary/40 to-transparent animate-shimmer"></div>
                    </div>
                )}

                {/* Success shimmer when AI completes writing */}
                {showCompletionNotice && !isLoading && (
                    <div className="absolute inset-x-0 top-0 h-1 overflow-hidden z-10">
                        <div
                            className="h-full w-1/3 bg-gradient-to-r from-transparent via-green-500/40 to-transparent animate-shimmer"></div>
                    </div>
                )}

                <div className="relative">
                    <Textarea
                        ref={textareaRef}
                        placeholder="Send a message..."
                        value={input}
                        onChange={handleInput}
                        className={cx(
                            'min-h[120px] max-h-[calc(55dvh)] overflow-scroll w-full resize-none rounded-2xl md:!text-sm bg-muted pb-10 dark:border-zinc-700',
                            isLoading && 'border-primary/30',
                            showCompletionNotice && !isLoading && 'border-green-500/30',
                            className,
                        )}
                        // rows={isMobile ? 4 : 4}
                        autoFocus
                        onKeyDown={(event) => {
                            if (event.key === 'Enter' && !event.shiftKey) {
                                event.preventDefault();

                                if (isLoading) {
                                    toast.error('Please wait for the model to finish its response!');
                                } else {
                                    submitForm();
                                }
                            }
                        }}
                    />

                    {/* AI writing indicator positioned at the top right of the textarea */}
                    {isLoading && (
                        <div className="absolute top-2 right-2 z-50 animate-fade-in">
                            <div
                                className="flex items-center gap-2 bg-background/80 backdrop-blur-sm border border-primary/20 text-foreground px-3 py-1.5 rounded-full shadow-sm">
                                <Loader2Icon size={12} className="animate-spin text-primary-foreground"/>
                                <span className="text-xs font-medium">magically is working...</span>
                            </div>
                        </div>
                    )}

                    {/* Success indicator positioned at the top right of the textarea */}
                    {showCompletionNotice && !isLoading && (
                        <div className="absolute top-2 right-2 z-50 animate-fade-in">
                            <div
                                className="flex items-center gap-2 bg-background/80 backdrop-blur-sm border border-green-500/20 text-foreground px-3 py-1.5 rounded-full shadow-sm">
                                <CheckCircleIcon size={12} className="text-green-500"/>
                                <span className="text-xs font-medium">Code ready</span>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="ml-1 h-5 w-5 p-0 rounded-full hover:bg-muted/50"
                                    onClick={() => setShowCompletionNotice(false)}
                                >
                                    <XIcon size={8}/>
                                    <span className="sr-only">Dismiss</span>
                                </Button>
                            </div>
                        </div>
                    )}
                </div>

            </div>

            <div className="absolute z-0 bottom-0 p-1 w-full flex flex-row justify-start gap-2 bg-gradient-to-t from-muted/50 to-transparent via-20%% border-input border-2 border-t-0 rounded-b-2xl backdrop-blur">
                <AddDropdownButton inDesignMode={inDesignMode} fileInputRef={fileInputRef} isLoading={isLoading} onVisualSelectionClicked={onVisualSelectionClicked}/>
                {/*<AttachmentsButton fileInputRef={fileInputRef} isLoading={isLoading}/>*/}
                {
                    !inDesignMode ?
                        <>
                            <SettingsDropdownButton projectId={projectId}/>
                            {/*<InspectButton onVisualSelectionClicked={onVisualSelectionClicked} selectMode={selectMode}/>*/}
                            {/*<PlanWithAIButton chatId={chatId} projectId={projectId} inDesignMode={inDesignMode} setInput={setInput}/>*/}
                        </> :
                    null
                }



                {/*<ModelSelector*/}
                {/*    selectedModelId={selectedModelId}*/}
                {/*    className="order-1 md:order-2"*/}
                {/*/>*/}
            </div>

            <div className="absolute bottom-0 right-0 p-2 w-fit flex flex-row justify-end">
                {isLoading ? (
                    <StopButton stop={stop} setMessages={setMessages}/>
                ) : (
                    <SendButton
                        input={input}
                        submitForm={submitForm}
                        uploadQueue={uploadQueue}
                    />
                )}
            </div>


        </div>
    );
}

export const MultimodalInput = memo(
    PureMultimodalInput,
    (prevProps, nextProps) => {
        // Always return false for componentContexts changes to ensure re-render
        // This is more reliable than deep equality checks for MobX observable arrays
        if (prevProps.componentContexts !== nextProps.componentContexts) return false;
        if (prevProps.componentContexts.length !== nextProps.componentContexts.length) return false;
        
        // Check other props
        if (prevProps.input !== nextProps.input) return false;
        if (prevProps.isLoading !== nextProps.isLoading) return false;
        if (prevProps.selectMode !== nextProps.selectMode) return false;
        if (prevProps.needsContinuation !== nextProps.needsContinuation) return false;
        if (prevProps.inDesignMode !== nextProps.inDesignMode) return false;
        if (!equal(prevProps.attachments, nextProps.attachments)) return false;
        if (!equal(prevProps.droppedFiles, nextProps.droppedFiles)) return false;

        return true;
    },
);

function PureAttachmentsButton({
                                   fileInputRef,
                                   isLoading,
                               }: {
    fileInputRef: React.MutableRefObject<HTMLInputElement | null>;
    isLoading: boolean;
}) {
    const isMobile = useIsMobile();

    return (
        <Button
            className="rounded-md rounded-bl-lg px-3 py-1 h-fit dark:border-zinc-700 hover:dark:bg-zinc-800/50 hover:bg-zinc-100 flex items-center gap-1.5 transition-colors"
            onClick={(event) => {
                event.preventDefault();
                fileInputRef.current?.click();
            }}
            disabled={isLoading}
            variant="ghost"
            size="sm"
        >
            <PaperclipIcon size={14}/>
            <span className="text-xs hidden md:inline">Attach</span>
        </Button>
    );
}

const AttachmentsButton = memo(PureAttachmentsButton);

function PureAddDropdownButton({
                                   fileInputRef,
                                   isLoading,
                                   onVisualSelectionClicked,
                                   inDesignMode
                               }: {
    fileInputRef: React.MutableRefObject<HTMLInputElement | null>;
    isLoading: boolean;
    onVisualSelectionClicked: () => void;
    inDesignMode: boolean;
}) {
    const isMobile = useIsMobile();

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button
                    className="rounded-md px-3 py-1 h-fit dark:border-zinc-700 hover:dark:bg-zinc-100 hover:border-zinc-700 flex items-center gap-1.5 transition-colors"
                    variant="ghost"
                    size="sm"
                >
                    <PlusIcon size={14}/>
                    <span className="text-xs inline">Add</span>
                    <ChevronDownIcon size={12} className="ml-0.5 inline"/>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-48">
                <DropdownMenuItem
                    onClick={() => fileInputRef.current?.click()}
                    className="cursor-pointer"
                >
                    <ImageIcon size={14}/>
                    <span>Image</span>
                </DropdownMenuItem>
                {
                    !inDesignMode ?
                        <DropdownMenuItem className="cursor-pointer" onClick={onVisualSelectionClicked}>
                            <Fullscreen size={14}/>
                            <span>Select elements</span>
                        </DropdownMenuItem>: null
                }

                {/*<DropdownMenuItem className="cursor-pointer" disabled={isLoading}*/}
                {/*>*/}
                {/*    /!*<DatabaseIcon size={14}/>*!/*/}
                {/*    <Image src={'/icons/integrations/supabase.png'} alt={'Supabase'} width={12} height={12}/>*/}
                {/*    <span>Supabase</span>*/}
                {/*</DropdownMenuItem>*/}
                {/*<DropdownMenuItem className="cursor-pointer">*/}
                {/*    <BrainIcon size={14}/>*/}
                {/*    <span>AI instructions</span>*/}
                {/*</DropdownMenuItem>*/}
                {/*<DropdownMenuSeparator/>*/}
                {/*<DropdownMenuItem className="cursor-pointer">*/}
                {/*    <PaperclipIcon size={14}/>*/}
                {/*    <span>File from URL</span>*/}
                {/*</DropdownMenuItem>*/}
            </DropdownMenuContent>
        </DropdownMenu>
    );
}

const AddDropdownButton = memo(PureAddDropdownButton);

function PureInspectButton({selectMode, onVisualSelectionClicked}: {
    selectMode: boolean, onVisualSelectionClicked: () => void;
}) {

    return (
        <Tooltip>
            <TooltipTrigger asChild>
                <Button 
                    className={`rounded-md hidden md:flex px-3 py-1 h-fit border items-center gap-1.5 transition-colors ${selectMode ? 'bg-zinc-100 dark:bg-zinc-100 border-zinc-700 text-zinc-700' : 'bg-transparent dark:bg-transparent dark:border-zinc-700 hover:bg-zinc-100 hover:dark:bg-zinc-100 hover:border-zinc-700'}`}
                    variant='ghost'
                    onClick={onVisualSelectionClicked}
                    size="sm"
                >
                    <BoxSelectIcon size={12}/>
                    Show AI
                </Button>
            </TooltipTrigger>
            <TooltipContent className="max-w-[250px]">
                Select a component on your screen to show the AI. This helps the AI understand exactly what you're talking about and provide more accurate help.
            </TooltipContent>
        </Tooltip>
    )
}

const InspectButton = memo(PureInspectButton)


function PureSettingsDropdownButton({projectId}: {projectId: string}) {
    const isMobile = useIsMobile();

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button
                    className="rounded-md px-3 py-1 h-fit dark:border-zinc-700 hover:dark:bg-zinc-100 hover:border-zinc-700 flex items-center gap-1.5 transition-colors"
                    variant="ghost"
                    size="sm"
                >
                    <Settings size={14}/>
                    <span className="text-xs">Settings</span>
                    <ChevronDownIcon size={12} className="ml-0.5 hidden md:inline"/>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-64">
                <DropdownMenuLabel>Preferences</DropdownMenuLabel>
                <DropdownMenuGroup>
                    <div className="px-2 py-1.5 pl-4">
                        <SoundSettings/>
                    </div>
                </DropdownMenuGroup>
                <DropdownMenuGroup>
                    <div className="px-2 py-1.5 pl-4">
                        <AgentModeSettings className="w-full"/>
                    </div>
                </DropdownMenuGroup>
                <DropdownMenuItem
                    className="cursor-pointer w-full py-1"
                >
                    <LinkButton variant="ghost" className="w-full justify-start"  size="sm" href={`/projects/${projectId}/deployments`}>
                        <Box className="h-4 w-4" />
                        <span>Deployments</span>
                    </LinkButton>
                </DropdownMenuItem>
                <DropdownMenuItem
                    className="cursor-pointer py-1"
                >
                    <LinkButton variant="ghost" className="w-full justify-start"  size="sm" href={`/projects/${projectId}/settings`}>
                        <Settings className="h-4 w-4" />
                        <span>Project Settings</span>
                    </LinkButton>
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}

const SettingsDropdownButton = memo(PureSettingsDropdownButton);

function PureStopButton({
                            stop,
                            setMessages,
                        }: {
    stop: () => void;
    setMessages: Dispatch<SetStateAction<Array<Message>>>;
}) {
    return (
        <div className="relative inline-flex">
            {/* Spinning border with more prominent colors */}
            <div
                className="absolute -inset-0.5 rounded-full bg-gradient-to-r from-red-600 via-yellow-300 to-red-600 animate-spin"/>
            {/* Button with background to cover spinning border except at edges */}
            <Button
                className="relative rounded-full p-1.5 h-fit bg-background dark:bg-background z-10 flex items-center gap-1.5"
                onClick={(event) => {
                    event.preventDefault();
                    stop();
                    setMessages((messages) => sanitizeUIMessages(messages));
                }}
                title="Stop AI writing"
            >
                <PauseIcon size={14} className="text-foreground"/>
            </Button>
        </div>
    );
}

const StopButton = memo(PureStopButton);

function PureSendButton({
                            submitForm,
                            input,
                            uploadQueue,
                        }: {
    submitForm: () => void;
    input: string;
    uploadQueue: Array<string>;
}) {
    return (
        <Button
            className="rounded-full p-1.5 h-fit border dark:border-zinc-600"
            onClick={(event) => {
                event.preventDefault();
                submitForm();
            }}
            disabled={input.length === 0 || uploadQueue.length > 0}
        >
            <ArrowUpIcon size={14}/>
        </Button>
    );
}

const SendButton = memo(PureSendButton, (prevProps, nextProps) => {
    if (prevProps.uploadQueue.length !== nextProps.uploadQueue.length)
        return false;
    if (prevProps.input !== nextProps.input) return false;
    return true;
});

function PurePlanWithAIButton({
    chatId,
    projectId,
    inDesignMode,
    setInput
}: {
    chatId: string;
    projectId: string;
    inDesignMode: boolean;
    setInput: (value: string) => void;
}) {
    const [isDiscussOpen, setIsDiscussOpen] = useState(false);

    // Don't show in design mode
    if (inDesignMode) return null;

    const handleApplySolution = (solution: string) => {
        // Apply the solution to the main chat input
        setInput(solution);
        setIsDiscussOpen(false);
        toast.success('Solution applied to main chat!');
    };

    return (
        <>
            <Tooltip>
                <TooltipTrigger asChild>
                    <div className="relative">
                        <Button
                            className="rounded-md px-3 py-1 h-fit border-0 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/50 dark:to-indigo-950/50 hover:from-blue-100 hover:to-indigo-100 dark:hover:from-blue-900/50 dark:hover:to-indigo-900/50 text-blue-700 dark:text-blue-300 flex items-center gap-1.5 transition-all duration-200 shadow-sm hover:shadow-md"
                            variant="ghost"
                            size="sm"
                            onClick={() => setIsDiscussOpen(true)}
                        >
                            <MessageSquareIcon size={12} className="text-blue-600 dark:text-blue-400" />
                            <span className="text-xs font-medium">Plan with AI</span>
                        </Button>
                        {/* NEW badge */}
                        <div className="absolute -top-1 -right-1 bg-gradient-to-r from-orange-500 to-red-500 text-white text-[9px] font-bold px-1.5 py-0.5 rounded-full shadow-sm">
                            NEW
                        </div>
                    </div>
                </TooltipTrigger>
                <TooltipContent className="max-w-[250px]">
                    <p>Start a focused discussion with AI to plan your next steps, review code, or get help with errors.</p>
                </TooltipContent>
            </Tooltip>

            <DiscussWithAI
                isOpen={isDiscussOpen}
                onClose={() => setIsDiscussOpen(false)}
                chatId={chatId}
                projectId={projectId}
                type="general-discussion"
                onApplySolution={handleApplySolution}
            />
        </>
    );
}

const PlanWithAIButton = memo(PurePlanWithAIButton);
