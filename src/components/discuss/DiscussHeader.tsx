import React, { memo } from 'react';
import { Button } from '@/components/ui/button';
import { X, Bug, FileText, Brain } from 'lucide-react';
import { observer } from 'mobx-react-lite';
import { DiscussStore, DiscussionType } from '@/stores/DiscussStore';

interface DiscussHeaderProps {
    discussStore: DiscussStore;
    onClose: () => void;
}

const getTypeIcon = (type: DiscussionType) => {
    switch (type) {
        case 'error-fix':
            return <Bug className="h-4 w-4" />;
        case 'code-review':
            return <FileText className="h-4 w-4" />;
        default:
            return <Brain className="h-4 w-4" />;
    }
};

const getTypeLabel = (type: DiscussionType) => {
    switch (type) {
        case 'error-fix':
            return 'Fix Error';
        case 'code-review':
            return 'Code Review';
        default:
            return 'General Discussion';
    }
};

const DiscussHeader = memo(observer(({ discussStore, onClose }: DiscussHeaderProps) => {
    const { state, discussChats, selectedChat } = discussStore;

    return (
        <div className="px-4 sm:px-6 py-3 border-b border-border/50 bg-background/50 backdrop-blur-sm flex-shrink-0">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                    <div className="h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center">
                        {getTypeIcon(state.selectedType)}
                    </div>
                    <div>
                        <h2 className="text-lg font-semibold text-foreground">Plan with AI</h2>
                        <p className="text-sm text-muted-foreground">
                            {getTypeLabel(state.selectedType)} • {discussChats.length} conversations
                            {state.selectedChatId && (
                                <span className="ml-2 text-primary">
                                    • {selectedChat ? 'Continuing' : 'Loading'}
                                </span>
                            )}
                        </p>
                    </div>
                </div>
                <Button
                    variant="ghost"
                    size="icon"
                    onClick={onClose}
                    className="h-8 w-8 rounded-full"
                >
                    <X className="h-4 w-4" />
                </Button>
            </div>
        </div>
    );
}));

DiscussHeader.displayName = 'DiscussHeader';

export default DiscussHeader;
