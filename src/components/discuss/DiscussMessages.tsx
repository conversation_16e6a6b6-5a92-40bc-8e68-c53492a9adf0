import React, { memo } from 'react';
import { <PERSON><PERSON>2, <PERSON><PERSON>, <PERSON>Tex<PERSON>, <PERSON> } from 'lucide-react';
import { observer } from 'mobx-react-lite';
import { DiscussStore, DiscussionType } from '@/stores/DiscussStore';
import { motion, AnimatePresence } from 'framer-motion';
import { Messages } from '@/components/base/messages';
import { Message } from 'ai';

interface DiscussMessagesProps {
    discussStore: DiscussStore;
    messages: Message[];
    isLoading: boolean;
    chatId: string;
    projectId: string;
    setMessages: any;
    append: any;
    status: any;
}

const getTypeIcon = (type: DiscussionType) => {
    switch (type) {
        case 'error-fix':
            return <Bug className="h-6 w-6" />;
        case 'code-review':
            return <FileText className="h-6 w-6" />;
        default:
            return <Brain className="h-6 w-6" />;
    }
};

const getTypeLabel = (type: DiscussionType) => {
    switch (type) {
        case 'error-fix':
            return 'Fix Error';
        case 'code-review':
            return 'Code Review';
        default:
            return 'General Discussion';
    }
};

const getEmptyStateMessage = (type: DiscussionType) => {
    switch (type) {
        case 'error-fix':
            return 'Describe the error you\'re experiencing in detail for better results.';
        case 'code-review':
            return 'Share your code for a comprehensive review and feedback.';
        default:
            return 'Start a conversation about your project, architecture, or any development questions.';
    }
};

const DiscussMessages = memo(observer(({
    discussStore,
    messages,
    isLoading,
    chatId,
    projectId,
    setMessages,
    append,
    status
}: DiscussMessagesProps) => {
    const { state } = discussStore;

    // Show loading state when switching chats
    if (state.isLoadingMessages && state.selectedChatId) {
        return (
            <div className="flex-1 overflow-y-auto bg-background/20 backdrop-blur-sm">
                <div className="p-4 sm:p-6 space-y-4">
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="text-center py-12"
                    >
                        <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                            <Loader2 className="h-6 w-6 animate-spin text-primary"/>
                        </div>
                        <h3 className="text-lg font-medium text-foreground mb-2">
                            Loading conversation...
                        </h3>
                        <p className="text-muted-foreground">
                            Fetching messages from your previous discussion
                        </p>
                    </motion.div>
                </div>
            </div>
        );
    }

    // Show empty state when no messages
    if (messages.length === 0) {
        return (
            <div className="flex-1 overflow-y-auto bg-background/20 backdrop-blur-sm">
                <div className="p-4 sm:p-6 space-y-4">
                    <AnimatePresence mode="wait">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -20 }}
                            className="text-center py-12"
                        >
                            <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                                {getTypeIcon(state.selectedType)}
                            </div>
                            <h3 className="text-lg font-medium text-foreground mb-2">
                                {getTypeLabel(state.selectedType)}
                            </h3>
                            <p className="text-muted-foreground max-w-md mx-auto">
                                {getEmptyStateMessage(state.selectedType)}
                            </p>
                        </motion.div>
                    </AnimatePresence>
                </div>
            </div>
        );
    }

    // Use the existing Messages component for rendering actual messages
    return (
        <div className="flex-1 overflow-y-auto bg-background/20 backdrop-blur-sm">
            <Messages
                chatId={chatId}
                projectId={projectId}
                isLoading={isLoading}
                votes={[]}
                messages={messages}
                setInput={() => {}} // Not used in discuss mode
                setMessages={setMessages}
                reload={async () => null} // Not used in discuss mode
                isReadonly={false}
                isBlockVisible={false}
                status={status}
                append={append} // Not used in discuss mode
                onVersionClick={() => {}} // Not used in discuss mode
                onActionClick={() => {}} // Not used in discuss mode
                setSnackError={() => {}} // Not used in discuss mode
                removeActions={true} // Remove actions in discuss mode
            />
        </div>
    );
}));

DiscussMessages.displayName = 'DiscussMessages';

export default DiscussMessages;
