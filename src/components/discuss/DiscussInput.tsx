import React, { memo } from 'react';
import { Button } from '@/components/ui/button';
import { Sparkles } from 'lucide-react';
import { motion } from 'framer-motion';
import { MultimodalInput } from '@/components/base/multimodal-input';
import { Message, Attachment } from 'ai';

interface DiscussInputProps {
    chatId: string;
    input: string;
    setInput: (value: string) => void;
    handleSubmit: (e: React.FormEvent) => void;
    isLoading: boolean;
    stop: () => void;
    attachments: Attachment[];
    setAttachments: (attachments: Attachment[]) => void;
    messages: Message[];
    append: (message: any) => void;
    onApplySolution?: () => void;
    showApplyButton: boolean;
}

const DiscussInput = memo(({
    chatId,
    input,
    setInput,
    handleSubmit,
    isLoading,
    stop,
    attachments,
    setAttachments,
    messages,
    append,
    onApplySolution,
    showApplyButton
}: DiscussInputProps) => {
    const handleApplySolution = () => {
        const lastAssistantMessage = [...messages].reverse().find(msg => msg.role === 'assistant');
        if (lastAssistantMessage && onApplySolution) {
            onApplySolution(lastAssistantMessage.content);
        }
    };

    return (
        <div className="border-t border-border/50 bg-background/50 backdrop-blur-sm flex-shrink-0">
            <div className="p-4 sm:p-6">
                <MultimodalInput
                    chatId={chatId}
                    projectId={`discuss-${chatId}`}
                    input={input}
                    setInput={setInput}
                    handleSubmit={handleSubmit}
                    isLoading={isLoading}
                    stop={stop}
                    attachments={attachments}
                    setAttachments={setAttachments}
                    messages={messages}
                    inDesignMode={true}
                    setMessages={() => {}}
                    append={append}
                    componentContexts={[]}
                    onRemoveComponentContext={() => {}}
                    onClearComponentContexts={() => {}}
                    onVisualSelectionClicked={() => {}}
                    selectMode={false}
                />

                {showApplyButton && onApplySolution && (
                    <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-4"
                    >
                        <Button
                            onClick={handleApplySolution}
                            variant="default"
                            className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 border-0 shadow-lg"
                        >
                            <Sparkles className="h-4 w-4 mr-2"/>
                            Apply Solution to Main Chat
                        </Button>
                    </motion.div>
                )}
            </div>
        </div>
    );
});

DiscussInput.displayName = 'DiscussInput';

export default DiscussInput;
