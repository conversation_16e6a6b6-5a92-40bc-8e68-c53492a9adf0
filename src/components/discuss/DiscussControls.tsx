import React, { memo } from 'react';
import { Button } from '@/components/ui/button';
import { Plus, ChevronDown, MessageSquare, Loader2, Bug, FileText, Brain } from 'lucide-react';
import { observer } from 'mobx-react-lite';
import { DiscussStore, DiscussionType } from '@/stores/DiscussStore';
import { cn } from '@/lib/utils';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';

interface DiscussControlsProps {
    discussStore: DiscussStore;
}

const getTypeIcon = (type: DiscussionType) => {
    switch (type) {
        case 'error-fix':
            return <Bug className="h-4 w-4" />;
        case 'code-review':
            return <FileText className="h-4 w-4" />;
        default:
            return <Brain className="h-4 w-4" />;
    }
};

const DiscussControls = memo(observer(({ discussStore }: DiscussControlsProps) => {
    const { state, discussChats, selectedChat } = discussStore;

    const handleChatSelect = (chatId: string) => {
        discussStore.selectChat(chatId);
    };

    const handleNewChat = () => {
        discussStore.createNewChat();
    };

    const handleTypeChange = (type: DiscussionType) => {
        discussStore.setSelectedType(type);
    };

    return (
        <div className="px-4 sm:px-6 py-3 border-b border-border/30 bg-background/30 backdrop-blur-sm flex-shrink-0">
            <div className="flex items-center gap-3">
                {/* Chat Selector */}
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button 
                            variant="outline" 
                            className={cn(
                                "flex items-center gap-2 min-w-[200px] justify-between",
                                state.isLoadingMessages && "opacity-50"
                            )}
                            disabled={state.isLoadingMessages}
                        >
                            <span className="truncate">
                                {state.isLoadingMessages ? (
                                    <span className="flex items-center gap-2">
                                        <Loader2 className="h-3 w-3 animate-spin" />
                                        Loading...
                                    </span>
                                ) : state.selectedChatId ? (
                                    selectedChat?.title || 'Untitled Chat'
                                ) : (
                                    'New Conversation'
                                )}
                            </span>
                            <ChevronDown className="h-4 w-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" className="w-[300px]">
                        <DropdownMenuItem onClick={handleNewChat} className="flex items-center gap-2">
                            <Plus className="h-4 w-4" />
                            <span>New Conversation</span>
                        </DropdownMenuItem>
                        {discussChats.length > 0 && (
                            <>
                                <DropdownMenuSeparator />
                                {discussChats.map((chat) => (
                                    <DropdownMenuItem 
                                        key={chat.id} 
                                        onClick={() => handleChatSelect(chat.id)}
                                        className={cn(
                                            "flex items-center gap-2",
                                            state.selectedChatId === chat.id && "bg-primary/10"
                                        )}
                                    >
                                        <MessageSquare className="h-4 w-4" />
                                        <div className="flex flex-col flex-1 min-w-0">
                                            <span className="truncate">{chat.title || 'Untitled Chat'}</span>
                                            <span className="text-xs text-muted-foreground">
                                                {new Date(chat.updatedAt).toLocaleDateString()}
                                            </span>
                                        </div>
                                    </DropdownMenuItem>
                                ))}
                            </>
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>

                {/* Type Selector */}
                <Select value={state.selectedType} onValueChange={handleTypeChange}>
                    <SelectTrigger className="w-[180px]">
                        <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="error-fix">
                            <div className="flex items-center gap-2">
                                <Bug className="h-4 w-4" />
                                <span>Fix Error</span>
                            </div>
                        </SelectItem>
                        <SelectItem value="code-review">
                            <div className="flex items-center gap-2">
                                <FileText className="h-4 w-4" />
                                <span>Code Review</span>
                            </div>
                        </SelectItem>
                        <SelectItem value="general-discussion">
                            <div className="flex items-center gap-2">
                                <Brain className="h-4 w-4" />
                                <span>General Discussion</span>
                            </div>
                        </SelectItem>
                    </SelectContent>
                </Select>
            </div>
        </div>
    );
}));

DiscussControls.displayName = 'DiscussControls';

export default DiscussControls;
