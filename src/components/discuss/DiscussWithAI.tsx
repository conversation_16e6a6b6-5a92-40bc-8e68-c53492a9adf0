import React, { useState, useRef, useEffect } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { toast } from 'sonner';
import { generateUUID } from '@/lib/utils';
import { FileNode } from '@/types/file';
import { useStores } from '@/stores/utils/useStores';
import { useChat } from 'ai/react';
import type { Message, Attachment } from 'ai';
import { observer } from 'mobx-react-lite';
import { DiscussionType } from '@/stores/DiscussStore';

// Import the new components
import DiscussHeader from './DiscussHeader';
import DiscussControls from './DiscussControls';
import DiscussMessages from './DiscussMessages';
import DiscussInput from './DiscussInput';

interface DiscussWithAIProps {
    isOpen: boolean;
    onClose: () => void;
    initialMessage?: string;
    type?: DiscussionType;
    onApplySolution?: (solution: string) => void;
    relevantFiles?: FileNode[];
    metadata?: Record<string, any>;
    chatId: string;
    projectId: string;
}

export const DiscussWithAI = observer(({
                                           isOpen,
                                           onClose,
                                           initialMessage = '',
                                           type = 'error-fix',
                                           onApplySolution,
                                           relevantFiles = [],
                                           metadata = {},
                                           chatId,
                                           projectId
                                       }: DiscussWithAIProps) => {
    const { generatorStore, logStore, discussStore } = useStores();
    const initialMessageSetRef = useRef(false);

    // Get active session if chatId is provided
    const session = chatId ? generatorStore.getActiveSession(chatId) : null;

    // State for attachments
    const [attachments, setAttachments] = useState<Attachment[]>([]);

    // Format initial message based on type
    const getFormattedPrompt = (message: string) => {
        if (discussStore.state.selectedType === 'error-fix') {
            return `I encountered an error: ${message}

Please analyze this error carefully and help me fix it. Consider:
1. The specific error message and its root cause
2. Any related code that might be affected
3. Dependencies that might be missing or misconfigured
4. Similar patterns in the codebase that work correctly
5. Provide a detailed solution with specific code changes

Please provide a detailed solution.`;
        } else if (discussStore.state.selectedType === 'code-review') {
            return `I'd like your opinion on this code: ${message}

Please review this code and provide feedback on:
1. Code quality and best practices
2. Potential bugs or edge cases
3. Performance considerations
4. Readability and maintainability
5. Suggestions for improvement

Please be specific in your feedback.`;
        }
        return message;
    };

    // Get relevant files from the session if not provided
    const filesToSend = relevantFiles.length > 0 ? relevantFiles : (session?.fileTree || []);

    // Initialize chat with existing messages if a chat is selected
    const {
        messages,
        input,
        setInput,
        handleSubmit,
        isLoading,
        append,
        stop,
        setMessages,
        status
    } = useChat({
        id: discussStore.currentChatId,
        api: '/api/chat',
        initialMessages: discussStore.currentMessages,
        body: {
            files: filesToSend,
            activeFile: '',
            dependencies: {},
            linkSupabaseProjectId: '',
            linkSupabaseConnection: '',
            projectId,
            logs: logStore.getLogs(chatId),
            agentModeEnabled: false,
            isReload: false,
            isInitial: false,
            componentContexts: [],
            isAutoFixed: false,
            isDiscussion: true,
            type: discussStore.state.selectedType,
            metadata
        },
        onError: (error) => {
            console.error('Error in discussion:', error);
            toast.error('Failed to get a response. Please try again.');
        }
    });

    // Open/close dialog effects
    useEffect(() => {
        if (isOpen && projectId) {
            discussStore.openDialog(projectId, type);
        } else if (!isOpen) {
            discussStore.closeDialog();
        }
    }, [isOpen, projectId, type, discussStore]);

    // Sync messages with store
    useEffect(() => {
        discussStore.setMessages(messages);
    }, [messages, discussStore]);

    // Refresh chats when new messages are sent
    useEffect(() => {
        if (messages.length > 0 && !isLoading) {
            discussStore.refreshChats();
        }
    }, [messages.length, isLoading, discussStore]);

    // Set initial message only once when dialog opens and no chat is selected
    useEffect(() => {
        if (isOpen && initialMessage && !initialMessageSetRef.current && messages.length === 0 && !discussStore.state.selectedChatId) {
            initialMessageSetRef.current = true;
            append({
                role: 'user',
                content: getFormattedPrompt(initialMessage),
                id: generateUUID()
            });
            setInput('');
        }
    }, [isOpen, initialMessage, messages.length, append, setInput, getFormattedPrompt, discussStore.state.selectedChatId]);


    const handleApplySolution = () => {
        const lastAssistantMessage = [...messages].reverse().find(msg => msg.role === 'assistant');
        if (lastAssistantMessage && onApplySolution) {
            onApplySolution(lastAssistantMessage.content);
            onClose();
            toast.success('Solution applied to main chat!');
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <DialogContent className="sm:max-w-[900px] h-[85vh] p-0 bg-background/95 backdrop-blur-xl border-border/50 overflow-hidden flex flex-col" disableCloseButton>
                <div className="relative flex flex-col h-full">
                    {/* Background gradient */}
                    <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-accent/5 to-chart-3/5" />

                    <div className="relative z-10 flex flex-col h-full">
                        <DiscussHeader discussStore={discussStore} onClose={onClose} />

                        <DiscussControls discussStore={discussStore} />

                        <DiscussMessages
                            discussStore={discussStore}
                            messages={messages}
                            isLoading={isLoading}
                            chatId={discussStore.currentChatId}
                            projectId={projectId || ''}
                            setMessages={setMessages}
                            append={append}
                            status={status}
                        />

                        <DiscussInput
                            chatId={discussStore.currentChatId}
                            input={input}
                            setInput={setInput}
                            handleSubmit={handleSubmit}
                            isLoading={isLoading}
                            stop={stop}
                            attachments={attachments}
                            setAttachments={setAttachments}
                            messages={messages}
                            append={append}
                            onApplySolution={handleApplySolution}
                            showApplyButton={messages.length > 0 && !!onApplySolution}
                        />

                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
});
